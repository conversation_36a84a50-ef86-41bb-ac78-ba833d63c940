import { NextResponse } from 'next/server';
import { AuditLog } from '@/lib/types';

async function logAudit(
  operation: string,
  details: string,
  userId: number,
  username: string,
) {
  const logEntry: AuditLog = {
    id: '', // Will be generated by the API
    timestamp: new Date(),
    userId,
    username,
    operation,
    details,
  };
  try {
    await fetch('http://localhost:3000/api/audit-logs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(logEntry),
    });
  } catch (error) {
    console.error('Failed to log audit entry:', error);
  }
}

export async function POST(request: Request) {
  const { userId, username, email } = await request.json();

  if (!userId || !username || !email) {
    return NextResponse.json(
      { message: 'User ID, username, and email are required' },
      { status: 400 },
    );
  }

  // Simulate sending a password reset email/phone confirmation
  console.log(
    `Simulating password reset for user ${username} (${email}). Confirmation sent.`,
  );

  await logAudit(
    'Password Reset Initiated',
    `Password reset initiated for user ${username} (${email}).`,
    userId,
    username,
  );

  return NextResponse.json({
    message: 'Password reset link sent successfully (simulated)',
  });
}
