'use client';

import { useState, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Save,
  Database,
  UploadCloud,
  DownloadCloud,
  Trash2,
  PlusCircle,
  RotateCw,
  Palette,
  Network,
} from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import AppearanceSettings from './appearance-settings';
import { ConnectionManager } from '@/components/connection-manager';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';

// --- Helper Functions for Color Conversion ---
const parseHsl = (hslStr: string): { h: number; s: number; l: number } => {
  if (!hslStr || typeof hslStr !== 'string') return { h: 0, s: 0, l: 0 };
  const [h, s, l] = hslStr.split(' ').map(parseFloat);
  return { h: h || 0, s: s || 0, l: l || 0 };
};

const formatHsl = (hslObj: { h: number; s: number; l: number }): string => {
  return `${hslObj.h.toFixed(1)} ${hslObj.s.toFixed(1)}% ${hslObj.l.toFixed(1)}%`;
};

const hslToHex = (h: number, s: number, l: number): string => {
  s /= 100;
  l /= 100;
  const a = s * Math.min(l, 1 - l);
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color)
      .toString(16)
      .padStart(2, '0');
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

const hexToHsl = (hex: string): { h: number; s: number; l: number } => {
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 4) {
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    r = parseInt(hex.substring(1, 3), 16);
    g = parseInt(hex.substring(3, 5), 16);
    b = parseInt(hex.substring(5, 7), 16);
  }
  r /= 255;
  g /= 255;
  b /= 255;
  const cmin = Math.min(r, g, b),
    cmax = Math.max(r, g, b),
    delta = cmax - cmin;
  let h = 0,
    s = 0,
    l = 0;

  if (delta === 0) h = 0;
  else if (cmax === r) h = ((g - b) / delta) % 6;
  else if (cmax === g) h = (b - r) / delta + 2;
  else h = (r - g) / delta + 4;
  h = Math.round(h * 60);
  if (h < 0) h += 360;
  l = (cmax + cmin) / 2;
  s = delta === 0 ? 0 : delta / (1 - Math.abs(2 * l - 1));
  s = +(s * 100);
  l = +(l * 100);
  return { h, s, l };
};
// --- End Helper Functions ---

function ColorPicker({
  label,
  value, // HSL string: "h s% l%"
  onChange,
}: {
  label: string;
  value: string;
  onChange: (value: string) => void;
}) {
  const [hsl, setHsl] = useState(parseHsl(value));

  useEffect(() => {
    setHsl(parseHsl(value));
  }, [value]);

  const handleColorInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newHex = e.target.value;
    const newHsl = hexToHsl(newHex);
    setHsl(newHsl);
    onChange(
      `${newHsl.h.toFixed(1)} ${newHsl.s.toFixed(1)}% ${newHsl.l.toFixed(1)}%`,
    );
  };

  const hexValue = hslToHex(hsl.h, hsl.s, hsl.l);

  return (
    <div className="space-y-2 p-4 border rounded-lg">
      <div className="flex justify-between items-center">
        <Label>{label}</Label>
        <div className="relative">
          <Input
            type="color"
            value={hexValue}
            onChange={handleColorInputChange}
            className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
          />
          <div
            className="w-8 h-8 rounded-full border"
            style={{ backgroundColor: hexValue }}
          />
        </div>
      </div>
      <div className="text-xs text-muted-foreground text-center font-mono">
        {hexValue}
      </div>
    </div>
  );
}

export default function SettingsPage() {
  const {
    currentUser,
    createBackupSnapshot,
    restoreFromSnapshot,
  } = useStore();
  const { toast } = useToast();
  const [isGoogleDriveSyncEnabled, setIsGoogleDriveSyncEnabled] =
    useState(false);
  const [backupFrequency, setBackupFrequency] = useState('weekly');
  const [selectedDatabase, setSelectedDatabase] =
    useState('temp_development_db'); // Default to temp DB
  const [newDatabaseName, setNewDatabaseName] = useState('');
  const [availableDatabases, setAvailableDatabases] = useState<string[]>([
    'temp_development_db', // Current temporary development database
    'main_db',
    'archive_db',
    'test_db',
  ]);
  const [availableBackups, setAvailableBackups] = useState<
    Array<{ id: string; name: string; timestamp: string; size: string }>
  >([]); // For storing available backups with metadata
  const [isCreatingBackup, setIsCreatingBackup] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [selectedBackupId, setSelectedBackupId] = useState<string>('');

  const permissions = currentUser?.permissions.settings;

  useEffect(() => {
    const root = window.document.documentElement;
    const style = getComputedStyle(root);
    setThemeColors({
      background: style.getPropertyValue('--background').trim(),
      foreground: style.getPropertyValue('--foreground').trim(),
      primary: style.getPropertyValue('--primary').trim(),
      accent: style.getPropertyValue('--accent').trim(),
    });
  }, []);

  const [themeColors, setThemeColors] = useState({
    background: '',
    foreground: '',
    primary: '',
    accent: '',
  });

  const handleColorChange = (
    colorName: keyof typeof themeColors,
    value: string,
  ) => {
    setThemeColors((prev) => ({ ...prev, [colorName]: value }));
  };

  const applyTheme = () => {
    const root = window.document.documentElement;
    root.style.setProperty('--background', themeColors.background);
    root.style.setProperty('--foreground', themeColors.foreground);
    root.style.setProperty('--primary', themeColors.primary);
    root.style.setProperty('--accent', themeColors.accent);

    // We would also save these settings to the backend here
    // For now, it's a client-side effect
    toast({
      title: 'تم تطبيق المظهر',
      description: 'تم تحديث ألوان النظام بنجاح.',
    });
  };

  // Load available backups on component mount
  useEffect(() => {
    loadAvailableBackups();
  }, []);

  const loadAvailableBackups = async () => {
    try {
      // For development mode, simulate some existing backups
      const mockBackups = [
        {
          id: 'backup_001',
          name: 'نسخة احتياطية تلقائية',
          timestamp: new Date(
            Date.now() - 24 * 60 * 60 * 1000,
          ).toISOString(), // 1 day ago
          size: '2.5 MB',
        },
        {
          id: 'backup_002',
          name: 'نسخة احتياطية يدوية',
          timestamp: new Date(
            Date.now() - 3 * 24 * 60 * 60 * 1000,
          ).toISOString(), // 3 days ago
          size: '2.1 MB',
        },
      ];

      // In development, use mock data
      if (selectedDatabase === 'temp_development_db') {
        setAvailableBackups(mockBackups);
        return;
      }

      // In production, make API call
      const response = await fetch(
        `/api/database/backups?database=${selectedDatabase}`,
      );
      if (response.ok) {
        const backups = await response.json();
        setAvailableBackups(backups);
      }
    } catch (error) {
      console.error('Error loading backups:', error);
      // Use mock data as fallback
      setAvailableBackups([]);
    }
  };



  const handleCreateDatabase = async () => {
    if (!newDatabaseName) {
      toast({
        title: 'خطأ',
        description: 'الرجاء إدخال اسم لقاعدة البيانات الجديدة.',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'جاري الإنشاء...',
        description: `جاري إنشاء قاعدة بيانات جديدة: ${newDatabaseName}`,
      });

      // Make API call to create database
      const response = await fetch('/api/database/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newDatabaseName }),
      });

      if (!response.ok) {
        throw new Error('فشل إنشاء قاعدة البيانات');
      }

      // Add the new database to the list
      setAvailableDatabases((prev) => [...prev, newDatabaseName]);

      toast({
        title: 'تم الإنشاء بنجاح',
        description: `تم إنشاء قاعدة بيانات جديدة: ${newDatabaseName}`,
      });

      setNewDatabaseName('');
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء إنشاء قاعدة البيانات',
        variant: 'destructive',
      });
    }
  };

  const handleChangeDatabaseName = async () => {
    if (!newDatabaseName) {
      toast({
        title: 'خطأ',
        description: 'الرجاء إدخال اسم جديد لقاعدة البيانات.',
        variant: 'destructive',
      });
      return;
    }
    if (newDatabaseName === selectedDatabase) {
      toast({
        title: 'تنبيه',
        description: 'الاسم الجديد هو نفس الاسم الحالي.',
        variant: 'default',
      });
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'جاري التغيير...',
        description: `جاري تغيير اسم ${selectedDatabase} إلى ${newDatabaseName}`,
      });

      // Make API call to rename database
      const response = await fetch('/api/database/rename', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          oldName: selectedDatabase,
          newName: newDatabaseName,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل تغيير اسم قاعدة البيانات');
      }

      // Update the list of available databases
      setAvailableDatabases((prev) =>
        prev.map((db) => (db === selectedDatabase ? newDatabaseName : db)),
      );

      toast({
        title: 'تم التغيير بنجاح',
        description: `تم تغيير اسم ${selectedDatabase} إلى ${newDatabaseName}`,
      });

      setSelectedDatabase(newDatabaseName); // Update selected DB to new name
      setNewDatabaseName(''); // Clear input
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء تغيير اسم قاعدة البيانات',
        variant: 'destructive',
      });
    }
  };

  const handleBackup = async () => {
    if (!selectedDatabase) {
      toast({
        title: 'خطأ',
        description: 'الرجاء اختيار قاعدة بيانات لأخذ نسخة احتياطية منها.',
        variant: 'destructive',
      });
      return;
    }

    setIsCreatingBackup(true);

    try {
      // Show loading toast
      toast({
        title: 'جاري النسخ الاحتياطي...',
        description: `جاري أخذ نسخة احتياطية من ${
          selectedDatabase === 'temp_development_db'
            ? 'قاعدة البيانات المؤقتة'
            : selectedDatabase
        }`,
      });

      // Handle temporary development database
      if (selectedDatabase === 'temp_development_db') {
        // Create actual backup from store data
        const backupData = createBackupSnapshot();

        // Save to localStorage for demo purposes
        const backupId = `backup_${Date.now()}`;
        localStorage.setItem(`backup_${backupId}`, JSON.stringify(backupData));

        // Update available backups list
        const newBackup = {
          id: backupId,
          name: 'نسخة احتياطية يدوية',
          timestamp: backupData.timestamp,
          size: `${(JSON.stringify(backupData).length / 1024 / 1024).toFixed(2)} MB`,
        };

        setAvailableBackups((prev) => [newBackup, ...prev]);

        toast({
          title: 'تم النسخ الاحتياطي بنجاح',
          description: `تم أخذ نسخة احتياطية من قاعدة البيانات المؤقتة`,
        });

        return;
      }

      // Make API call to backup database (for production databases)
      const response = await fetch('/api/database/backup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          databaseName: selectedDatabase,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error('فشل إنشاء النسخة الاحتياطية');
      }

      const data = await response.json();

      // Update the list of available backups if the API returns them
      if (data.backups) {
        setAvailableBackups(data.backups);
      } else {
        await loadAvailableBackups(); // Reload backups
      }

      toast({
        title: 'تم النسخ الاحتياطي بنجاح',
        description: `تم أخذ نسخة احتياطية من ${selectedDatabase}`,
      });
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء إنشاء النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsCreatingBackup(false);
    }
  };

  const handleRestore = async () => {
    if (!selectedDatabase) {
      toast({
        title: 'خطأ',
        description: 'الرجاء اختيار قاعدة بيانات للاستعادة إليها.',
        variant: 'destructive',
      });
      return;
    }

    if (!selectedBackupId && selectedDatabase !== 'temp_development_db') {
      toast({
        title: 'خطأ',
        description: 'الرجاء اختيار نسخة احتياطية للاستعادة منها.',
        variant: 'destructive',
      });
      return;
    }

    // Confirm restoration with user
    const selectedBackup = availableBackups.find(
      (b) => b.id === selectedBackupId,
    );
    const confirmMessage = `هل أنت متأكد من استعادة النسخة الاحتياطية: ${
      selectedBackup?.name || 'الأحدث'
    }؟\n\nسيتم استبدال جميع البيانات الحالية. هذا الإجراء لا يمكن التراجع عنه.\n\nينصح بأخذ نسخة احتياطية من البيانات الحالية أولاً.`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    setIsRestoring(true);

    try {
      // Show loading toast
      toast({
        title: 'جاري الاستعادة...',
        description: `جاري استعادة نسخة احتياطية لـ ${
          selectedDatabase === 'temp_development_db'
            ? 'قاعدة البيانات المؤقتة'
            : selectedDatabase
        }`,
      });

      // Handle temporary development database
      if (selectedDatabase === 'temp_development_db') {
        // Get the selected backup or latest one
        const backupToRestore =
          selectedBackupId ||
          (availableBackups.length > 0 ? availableBackups[0].id : null);

        if (!backupToRestore) {
          throw new Error('لا توجد نسخ احتياطية متاحة للاستعادة');
        }

        // Get backup data from localStorage
        const backupDataString = localStorage.getItem(
          `backup_${backupToRestore}`,
        );
        if (!backupDataString) {
          throw new Error('لم يتم العثور على النسخة الاحتياطية المحددة');
        }

        const backupData = JSON.parse(backupDataString);

        // Handle different backup formats
        let dataToRestore;
        let backupTimestamp = new Date().toISOString();

        if (backupData.data && backupData.timestamp) {
          // New format with data wrapper
          dataToRestore = backupData.data;
          backupTimestamp = backupData.timestamp;
        } else if (
          backupData.timestamp &&
          (backupData.devices ||
            backupData.warehouses ||
            backupData.stocktakes)
        ) {
          // Old format with direct data
          dataToRestore = backupData;
          backupTimestamp = backupData.timestamp;
        } else if (
          typeof backupData === 'object' &&
          (backupData.devices || backupData.warehouses || backupData.stocktakes)
        ) {
          // Data without wrapper
          dataToRestore = backupData;
          if (backupData.timestamp) {
            backupTimestamp = backupData.timestamp;
          }
        } else {
          throw new Error('تنسيق النسخة الاحتياطية غير مدعوم');
        }

        // Restore data to store using the actual restore function
        try {
          restoreFromSnapshot({ data: dataToRestore });

          toast({
            title: 'تمت الاستعادة بنجاح',
            description: `تم استعادة نسخة احتياطية لقاعدة البيانات المؤقتة من ${new Date(
              backupTimestamp,
            ).toLocaleDateString('ar-SA')}`,
          });
        } catch (restoreError) {
          console.error('Restore error:', restoreError);
          throw new Error(
            'فشل في استعادة البيانات: ' +
              (restoreError instanceof Error
                ? restoreError.message
                : 'خطأ غير معروف'),
          );
        }

        return;
      }

      // Make API call to restore database (for production databases)
      const response = await fetch('/api/database/restore', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          databaseName: selectedDatabase,
          backupId: selectedBackupId || 'latest',
        }),
      });

      if (!response.ok) {
        throw new Error('فشل استعادة النسخة الاحتياطية');
      }

      toast({
        title: 'تمت الاستعادة بنجاح',
        description: `تم استعادة نسخة احتياطية لـ ${selectedDatabase}`,
      });

      // Reload available backups
      await loadAvailableBackups();
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء استعادة النسخة الاحتياطية',
        variant: 'destructive',
      });
    } finally {
      setIsRestoring(false);
    }
  };

  const handleExportBackup = () => {
    if (!selectedBackupId) {
      toast({
        title: 'خطأ',
        description: 'الرجاء اختيار نسخة احتياطية للتصدير.',
        variant: 'destructive',
      });
      return;
    }

    try {
      const backupDataString = localStorage.getItem(`backup_${selectedBackupId}`);
      if (!backupDataString) {
        throw new Error('لم يتم العثور على النسخة الاحتياطية');
      }

      const backup = availableBackups.find((b) => b.id === selectedBackupId);
      const fileName = `backup_${selectedDatabase}_${new Date()
        .toISOString()
        .split('T')[0]}.json`;

      const blob = new Blob([backupDataString], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: 'تم التصدير بنجاح',
        description: `تم تصدير النسخة الاحتياطية: ${backup?.name}`,
      });
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء تصدير النسخة الاحتياطية',
        variant: 'destructive',
      });
    }
  };

  const handleImportBackup = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const backupData = JSON.parse(content);

        // More flexible validation for backup structure
        let isValidBackup = false;
        let backupTimestamp = new Date().toISOString();

        // Check if it's the new format (with data property)
        if (backupData.data && backupData.timestamp) {
          isValidBackup = true;
          backupTimestamp = backupData.timestamp;
        }
        // Check if it's the old format (direct data with timestamp)
        else if (backupData.timestamp) {
          isValidBackup = true;
          backupTimestamp = backupData.timestamp;
        }
        // Check if it's a basic data export
        else if (
          typeof backupData === 'object' &&
          (backupData.devices || backupData.warehouses || backupData.stocktakes)
        ) {
          isValidBackup = true;
          // Add timestamp if missing
          if (!backupData.timestamp) {
            backupData.timestamp = new Date().toISOString();
            backupTimestamp = backupData.timestamp;
          }
        }

        if (!isValidBackup) {
          throw new Error(
            'ملف النسخة الاحتياطية غير صالح أو لا يحتوي على بيانات معروفة',
          );
        }

        // Generate new backup ID
        const backupId = `backup_imported_${Date.now()}`;
        localStorage.setItem(`backup_${backupId}`, content);

        // Add to available backups
        const newBackup = {
          id: backupId,
          name: `نسخة مستوردة - ${file.name}`,
          timestamp: backupTimestamp,
          size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        };

        setAvailableBackups((prev) => [newBackup, ...prev]);

        toast({
          title: 'تم الاستيراد بنجاح',
          description: `تم استيراد النسخة الاحتياطية: ${file.name}`,
        });

        // Clear the input
        event.target.value = '';
      } catch (error) {
        toast({
          title: 'خطأ في الاستيراد',
          description:
            error instanceof Error
              ? error.message
              : 'حدث خطأ أثناء استيراد النسخة الاحتياطية. تأكد من أن الملف صحيح.',
          variant: 'destructive',
        });

        // Clear the input even on error
        event.target.value = '';
      }
    };

    reader.readAsText(file);
  };

  const handleDeleteDatabase = async () => {
    if (!selectedDatabase) {
      toast({
        title: 'خطأ',
        description: 'الرجاء اختيار قاعدة بيانات لحذفها.',
        variant: 'destructive',
      });
      return;
    }

    // Confirm deletion with the user
    if (
      !window.confirm(
        `هل أنت متأكد من حذف قاعدة البيانات ${selectedDatabase}؟ هذا الإجراء لا يمكن التراجع عنه.`,
      )
    ) {
      return;
    }

    try {
      // Show loading toast
      toast({
        title: 'جاري الحذف...',
        description: `جاري حذف قاعدة البيانات ${selectedDatabase}`,
        variant: 'destructive',
      });

      // Make API call to delete database
      const response = await fetch('/api/database/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          databaseName: selectedDatabase,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل حذف قاعدة البيانات');
      }

      // Remove the deleted database from the list
      setAvailableDatabases((prev) => prev.filter((db) => db !== selectedDatabase));

      toast({
        title: 'تم الحذف بنجاح',
        description: `تم حذف قاعدة البيانات ${selectedDatabase}`,
        variant: 'destructive',
      });

      // Reset selected database if it was deleted
      if (
        availableDatabases.length > 0 &&
        selectedDatabase === availableDatabases[0]
      ) {
        setSelectedDatabase(availableDatabases[1] || availableDatabases[0]);
      } else if (availableDatabases.length > 0) {
        setSelectedDatabase(availableDatabases[0]);
      } else {
        setSelectedDatabase('');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description:
          error instanceof Error
            ? error.message
            : 'حدث خطأ أثناء حذف قاعدة البيانات',
        variant: 'destructive',
      });
    }
  };

  if (!permissions?.view) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>غير مصرح بالدخول</CardTitle>
        </CardHeader>
        <CardContent>
          <p>ليس لديك الصلاحيات اللازمة لعرض هذه الصفحة.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Tabs defaultValue="appearance" className="w-full max-w-4xl mx-auto">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="appearance">المظهر</TabsTrigger>
        <TabsTrigger value="colors">الألوان والمظهر</TabsTrigger>
        <TabsTrigger value="connection">إدارة الاتصال</TabsTrigger>
        <TabsTrigger value="database">قواعد البيانات</TabsTrigger>
      </TabsList>
      <TabsContent value="appearance" className="mt-4">
        <AppearanceSettings />
      </TabsContent>
      <TabsContent value="colors" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette />
              الألوان والمظهر
            </CardTitle>
            <CardDescription>
              تخصيص ألوان النظام والتحكم في الوضع الليلي والنهاري.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <ColorPicker
                label="اللون الأساسي"
                value={themeColors.primary}
                onChange={(value) => handleColorChange('primary', value)}
              />
              <ColorPicker
                label="لون الخلفية"
                value={themeColors.background}
                onChange={(value) => handleColorChange('background', value)}
              />
              <ColorPicker
                label="لون النص"
                value={themeColors.foreground}
                onChange={(value) => handleColorChange('foreground', value)}
              />
              <ColorPicker
                label="لون التمييز"
                value={themeColors.accent}
                onChange={(value) => handleColorChange('accent', value)}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end">
            <Button onClick={applyTheme}>
              <Save className="ml-2 h-4 w-4" />
              تطبيق الألوان
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      <TabsContent value="connection" className="mt-4">
        <ConnectionManager />
      </TabsContent>
      <TabsContent value="database" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database />
              إدارة قواعد البيانات والنسخ الاحتياطي
            </CardTitle>
            <CardDescription>
              إدارة النسخ الاحتياطي والاستعادة والمزامنة.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4 p-4 border rounded-lg">
              <h4 className="font-semibold">إجراءات النسخ الاحتياطي والاستعادة</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="select-db">اختيار قاعدة البيانات</Label>
                  <Select
                    value={selectedDatabase}
                    onValueChange={setSelectedDatabase}
                  >
                    <SelectTrigger id="select-db">
                      <SelectValue placeholder="اختر قاعدة بيانات" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDatabases.map((db) => (
                        <SelectItem key={db} value={db}>
                          {db === 'temp_development_db'
                            ? 'قاعدة البيانات المؤقتة (التطوير)'
                            : db}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedDatabase === 'temp_development_db' && (
                    <p className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
                      🔧 أنت تعمل حالياً على قاعدة بيانات مؤقتة. النسخ الاحتياطي والاستعادة متاحان للبيانات الحالية.
                    </p>
                  )}
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-db-name">
                    تغيير اسم قاعدة البيانات المختارة
                  </Label>
                  <Input
                    id="new-db-name"
                    value={newDatabaseName}
                    onChange={(e) => setNewDatabaseName(e.target.value)}
                    placeholder="أدخل الاسم الجديد"
                    disabled={selectedDatabase === 'temp_development_db'}
                  />
                  {selectedDatabase === 'temp_development_db' && (
                    <p className="text-xs text-gray-500">
                      لا يمكن تغيير اسم قاعدة البيانات المؤقتة
                    </p>
                  )}
                </div>
              </div>

              {availableBackups.length > 0 && (
                <div className="space-y-2">
                  <Label>النسخ الاحتياطية المتاحة</Label>
                  <div className="border rounded-lg p-3 max-h-32 overflow-y-auto">
                    {availableBackups.map((backup) => (
                      <div
                        key={backup.id}
                        className={cn(
                          'flex items-center justify-between p-2 rounded cursor-pointer hover:bg-gray-50 transition-colors',
                          selectedBackupId === backup.id &&
                            'bg-blue-50 border border-blue-200',
                        )}
                        onClick={() => setSelectedBackupId(backup.id)}
                      >
                        <div className="flex-1">
                          <div className="font-medium text-sm">{backup.name}</div>
                          <div className="text-xs text-gray-500">
                            {new Date(backup.timestamp).toLocaleDateString(
                              'ar-SA',
                              {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit',
                              },
                            )}
                          </div>
                        </div>
                        <div className="text-xs text-gray-400">
                          {backup.size}
                        </div>
                      </div>
                    ))}
                  </div>
                  {selectedBackupId && (
                    <div className="bg-green-50 border border-green-200 rounded p-2">
                      <p className="text-xs text-green-600">
                        ✓ تم اختيار:{' '}
                        {availableBackups.find((b) => b.id === selectedBackupId)
                          ?.name}
                      </p>
                      <p className="text-xs text-green-500 mt-1">
                        💡 نصيحة: تأكد من أخذ نسخة احتياطية من البيانات الحالية قبل الاستعادة
                      </p>
                    </div>
                  )}
                </div>
              )}

              <div className="flex flex-wrap gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleCreateDatabase()}
                  disabled={selectedDatabase === 'temp_development_db'}
                >
                  <PlusCircle className="ml-2 h-4 w-4" />
                  إنشاء قاعدة بيانات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleChangeDatabaseName()}
                  disabled={
                    !newDatabaseName || selectedDatabase === 'temp_development_db'
                  }
                >
                  <RotateCw className="ml-2 h-4 w-4" />
                  تغيير اسم قاعدة البيانات
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleBackup()}
                  disabled={isCreatingBackup}
                >
                  <DownloadCloud className="ml-2 h-4 w-4" />
                  {isCreatingBackup ? 'جاري النسخ...' : 'أخذ نسخة احتياطية'}
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => handleRestore()}
                  disabled={isRestoring || availableBackups.length === 0}
                >
                  <UploadCloud className="ml-2 h-4 w-4" />
                  {isRestoring ? 'جاري الاستعادة...' : 'استعادة نسخة احتياطية'}
                </Button>
              </div>

              {selectedDatabase === 'temp_development_db' && (
                <div className="border-t pt-4 space-y-3">
                  <h5 className="font-medium">تصدير واستيراد النسخ الاحتياطية</h5>
                  <div className="flex flex-wrap gap-2">
                    <Button
                      variant="outline"
                      onClick={handleExportBackup}
                      disabled={!selectedBackupId}
                      size="sm"
                    >
                      <Save className="ml-2 h-4 w-4" />
                      تصدير النسخة المختارة
                    </Button>
                    <Button
                      variant="outline"
                      onClick={() =>
                        document.getElementById('backup-import')?.click()
                      }
                      size="sm"
                    >
                      <UploadCloud className="ml-2 h-4 w-4" />
                      استيراد نسخة احتياطية
                    </Button>
                    <input
                      id="backup-import"
                      type="file"
                      accept=".json"
                      onChange={handleImportBackup}
                      className="hidden"
                    />
                  </div>
                </div>
              )}

              <div className="border-t pt-4">
                <h5 className="font-medium text-red-600 mb-2">منطقة خطر</h5>
                <Button
                  variant="destructive"
                  onClick={() => handleDeleteDatabase()}
                  disabled={selectedDatabase === 'temp_development_db'}
                  size="sm"
                >
                  <Trash2 className="ml-2 h-4 w-4" />
                  حذف قاعدة البيانات
                </Button>
                {selectedDatabase === 'temp_development_db' && (
                  <p className="text-xs text-gray-500 mt-1">
                    لا يمكن حذف قاعدة البيانات المؤقتة
                  </p>
                )}
              </div>

              {selectedDatabase === 'temp_development_db' && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <h5 className="font-medium text-yellow-800 mb-2">
                    💡 معلومات هامة عن النسخ الاحتياطي:
                  </h5>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• النسخ الاحتياطية تُحفظ محلياً في المتصفح</li>
                    <li>• البيانات المؤقتة قد تُفقد عند إعادة تحميل الصفحة</li>
                    <li>• يمكن استيراد وتصدير النسخ الاحتياطية كملفات</li>
                    <li>• الاستعادة تستبدل جميع البيانات الحالية</li>
                    <li>• ينصح بأخذ نسخة احتياطية قبل الاستعادة</li>
                  </ul>
                  <div className="mt-2 p-2 bg-yellow-100 rounded text-xs text-yellow-600">
                    <strong>تلميح:</strong> إذا واجهت خطأ "النسخة غير صالحة"، تأكد من أن الملف تم تصديره من نفس النظام أو أنه يحتوي على البيانات المطلوبة.
                  </div>
                </div>
              )}
            </div>
            <div className="space-y-4 p-4 border rounded-lg">
              <h4 className="font-semibold">
                المزامنة التلقائية مع Google Drive
              </h4>
              <div className="flex items-center justify-between">
                <Label htmlFor="gdrive-sync">تفعيل المزامنة التلقائية</Label>
                <Switch
                  id="gdrive-sync"
                  checked={isGoogleDriveSyncEnabled}
                  onCheckedChange={setIsGoogleDriveSyncEnabled}
                />
              </div>
              <div className="space-y-2">
                <Label>فترة النسخ الاحتياطي التلقائي</Label>
                <Select
                  dir="rtl"
                  value={backupFrequency}
                  onValueChange={(value: string) => setBackupFrequency(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفترة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">يوميًا</SelectItem>
                    <SelectItem value="weekly">أسبوعيًا</SelectItem>
                    <SelectItem value="monthly">شهريًا</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                onClick={() =>
                  toast({
                    title: 'تمت محاكاة ربط حساب Google Drive',
                    duration: 3000,
                  })
                }
              >
                ربط حساب Google Drive
              </Button>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
