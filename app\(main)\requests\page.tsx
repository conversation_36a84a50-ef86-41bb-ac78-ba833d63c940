'use client';

import { useState } from 'react';
import { useStore } from '@/context/store';
import { useRouter } from 'next/navigation';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Eye, CheckCircle, XCircle, Info, ExternalLink } from 'lucide-react';
import type { EmployeeRequest } from '@/lib/types';
import { Label } from '@/components/ui/label';

export default function RequestsPage() {
  const { employeeRequests, processEmployeeRequest, currentUser } = useStore();
  const router = useRouter();

  const [selectedRequest, setSelectedRequest] =
    useState<EmployeeRequest | null>(null);
  const [adminNotes, setAdminNotes] = useState('');

  const permissions = currentUser?.permissions.requests;

  const getPriorityBadge = (priority: EmployeeRequest['priority']) => {
    switch (priority) {
      case 'طاريء':
        return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/20';
      case 'طاريء جدا':
        return 'bg-red-500/20 text-red-400 border-red-500/20';
      default:
        return 'bg-blue-500/20 text-blue-400 border-blue-500/20';
    }
  };

  const handleOpenRequest = (request: EmployeeRequest) => {
    setSelectedRequest(request);
    setAdminNotes(request.adminNotes || '');
  };

  const handleProcessRequest = (status: 'approved' | 'rejected') => {
    if (!selectedRequest) return;
    if (status === 'rejected' && !adminNotes.trim()) {
      toast({
        title: 'مطلوب',
        description: 'يرجى كتابة سبب الرفض في الملاحظات.',
        variant: 'destructive',
      });
      return;
    }
    processEmployeeRequest(selectedRequest.id, status, adminNotes);
    setSelectedRequest(null);
  };

  const handleNavigateToOrder = () => {
    if (!selectedRequest) return;

    let path = '';
    switch (selectedRequest.relatedOrderType) {
      case 'supply':
        path = '/supply';
        break;
      case 'sale':
        path = '/sales';
        break;
      case 'return':
        path = '/returns';
        break;
      case 'evaluation':
        path = '/grading';
        break;
      case 'maintenance':
        path = '/maintenance';
        break;
      case 'warehouse_transfer':
        path = '/warehouse-transfer';
        break;
    }

    if (path) {
      // This is a simplified navigation. A full solution would need to also pass the order ID
      // and have the target page load it automatically.
      router.push(path);
      setSelectedRequest(null);
    }
  };

  const renderRequestsTable = (requests: EmployeeRequest[]) => (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>رقم الطلب</TableHead>
            <TableHead>الموظف</TableHead>
            <TableHead>الأمر المرتبط</TableHead>
            <TableHead>نوع الطلب</TableHead>
            <TableHead>الأولوية</TableHead>
            <TableHead>تاريخ الطلب</TableHead>
            <TableHead>إجراء</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {requests.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                لا توجد طلبات في هذا القسم.
              </TableCell>
            </TableRow>
          ) : (
            requests.map((req) => (
              <TableRow key={req.id}>
                <TableCell>{req.requestNumber}</TableCell>
                <TableCell>{req.employeeName}</TableCell>
                <TableCell>
                  <Badge variant="secondary">{req.relatedOrderDisplayId}</Badge>
                </TableCell>
                <TableCell>{req.requestType}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={getPriorityBadge(req.priority)}
                  >
                    {req.priority}
                  </Badge>
                </TableCell>
                <TableCell>
                  {format(new Date(req.requestDate), 'yyyy/MM/dd HH:mm')}
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleOpenRequest(req)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );

  if (!permissions?.view) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>غير مصرح بالدخول</CardTitle>
        </CardHeader>
        <CardContent>
          <p>ليس لديك الصلاحيات اللازمة لعرض هذه الصفحة.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Tabs defaultValue="pending" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="pending">طلبات قيد المراجعة</TabsTrigger>
          <TabsTrigger value="approved">طلبات منفذة</TabsTrigger>
          <TabsTrigger value="rejected">طلبات مرفوضة</TabsTrigger>
        </TabsList>
        <TabsContent value="pending" className="mt-4">
          {renderRequestsTable(
            employeeRequests.filter((r) => r.status === 'قيد المراجعة'),
          )}
        </TabsContent>
        <TabsContent value="approved" className="mt-4">
          {renderRequestsTable(
            employeeRequests.filter((r) => r.status === 'تم التنفيذ'),
          )}
        </TabsContent>
        <TabsContent value="rejected" className="mt-4">
          {renderRequestsTable(
            employeeRequests.filter((r) => r.status === 'مرفوض'),
          )}
        </TabsContent>
      </Tabs>

      <Dialog
        open={!!selectedRequest}
        onOpenChange={(open) => !open && setSelectedRequest(null)}
      >
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              مراجعة الطلب: {selectedRequest?.requestNumber}
            </DialogTitle>
            <DialogDescription>
              من: {selectedRequest?.employeeName} - بتاريخ:{' '}
              {selectedRequest &&
                format(new Date(selectedRequest.requestDate), 'yyyy/MM/dd')}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="grid grid-cols-3 gap-4">
              <div>
                <span className="font-semibold">الأمر:</span>{' '}
                {selectedRequest?.relatedOrderDisplayId}
              </div>
              <div>
                <span className="font-semibold">النوع:</span>{' '}
                {selectedRequest?.requestType}
              </div>
              <div>
                <span className="font-semibold">الأولوية:</span>{' '}
                <Badge
                  variant="outline"
                  className={getPriorityBadge(selectedRequest?.priority as any)}
                >
                  {selectedRequest?.priority}
                </Badge>
              </div>
            </div>
            <Card>
              <CardHeader>
                <CardTitle className="text-base">تفاصيل المشكلة</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm">{selectedRequest?.notes}</p>
                {selectedRequest?.attachmentName && (
                  <p className="text-xs text-muted-foreground mt-2">
                    المرفق: {selectedRequest.attachmentName}
                  </p>
                )}
              </CardContent>
            </Card>
            {selectedRequest?.status !== 'قيد المراجعة' && (
              <Card
                className={
                  selectedRequest?.status === 'مرفوض'
                    ? 'border-destructive'
                    : 'border-green-500'
                }
              >
                <CardHeader>
                  <CardTitle className="text-base">رد الإدارة</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">
                    {selectedRequest?.adminNotes || 'لا توجد ملاحظات.'}
                  </p>
                  <p className="text-xs text-muted-foreground mt-2">
                    الحالة: {selectedRequest?.status} - بتاريخ:{' '}
                    {selectedRequest?.resolutionDate &&
                      format(
                        new Date(selectedRequest.resolutionDate),
                        'yyyy/MM/dd',
                      )}
                  </p>
                </CardContent>
              </Card>
            )}
            {selectedRequest?.status === 'قيد المراجعة' && permissions.edit && (
              <div className="space-y-2">
                <Label htmlFor="admin-notes">
                  ملاحظات الإدارة (إلزامية عند الرفض)
                </Label>
                <Textarea
                  id="admin-notes"
                  value={adminNotes}
                  onChange={(e) => setAdminNotes(e.target.value)}
                  placeholder="في حال رفض الطلب، يرجى كتابة السبب هنا..."
                />
              </div>
            )}
          </div>
          <DialogFooter className="justify-between">
            <div>
              <Button variant="outline" onClick={handleNavigateToOrder}>
                <ExternalLink className="ml-2 h-4 w-4" />
                فتح الأمر المرتبط
              </Button>
            </div>
            <div className="flex gap-2">
              {selectedRequest?.status === 'قيد المراجعة' &&
              permissions.edit ? (
                <>
                  <Button
                    variant="destructive"
                    onClick={() => handleProcessRequest('rejected')}
                    disabled={!adminNotes.trim()}
                  >
                    <XCircle className="ml-2 h-4 w-4" />
                    رفض الطلب
                  </Button>
                  <Button
                    onClick={() => handleProcessRequest('approved')}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <CheckCircle className="ml-2 h-4 w-4" />
                    تم التنفيذ
                  </Button>
                </>
              ) : (
                <DialogClose asChild>
                  <Button variant="outline">إغلاق</Button>
                </DialogClose>
              )}
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
