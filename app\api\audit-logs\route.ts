import { NextResponse } from 'next/server';
import { AuditLog } from '@/lib/types';

let auditLogs: AuditLog[] = [];

export async function GET() {
  return NextResponse.json(auditLogs);
}

export async function POST(request: Request) {
  const newLog: AuditLog = await request.json();
  newLog.id = `log-${Date.now()}`;
  newLog.date = new Date();
  auditLogs.push(newLog);
  return NextResponse.json(newLog, { status: 201 });
}
