import { NextResponse } from 'next/server';
import { SystemSettings } from '@/lib/types';

// Mock data - في التطبيق الحقيقي، ستأتي من قاعدة البيانات
let systemSettings: SystemSettings = {
  logoUrl: '',
  companyNameAr: 'Device<PERSON>low',
  companyNameEn: 'DeviceFlow',
  addressAr: 'الشارع الرئيسي، المدينة، الدولة',
  addressEn: 'Main Street, City, Country',
  phone: '+************',
  email: '<EMAIL>',
  website: 'www.deviceflow.com',
  footerTextAr: 'شكرًا لتعاملكم معنا.',
  footerTextEn: 'Thank you for your business.',
};

export async function GET() {
  try {
    return NextResponse.json(systemSettings);
  } catch (error) {
    console.error('Error fetching settings:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الإعدادات' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const data: Partial<SystemSettings> = await request.json();
    
    // التحقق من صحة البيانات
    if (!data) {
      return NextResponse.json(
        { error: 'لم يتم إرسال بيانات' },
        { status: 400 }
      );
    }

    // تحديث الإعدادات
    systemSettings = { ...systemSettings, ...data };

    // في التطبيق الحقيقي، ستحفظ في قاعدة البيانات هنا
    // await prisma.systemSetting.update({
    //   where: { id: 1 },
    //   data,
    // });

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الإعدادات بنجاح',
      data: systemSettings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الإعدادات' },
      { status: 500 }
    );
  }
}
