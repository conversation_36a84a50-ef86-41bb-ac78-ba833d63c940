import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const section = formData.get('section') as string;

    if (!files || files.length === 0) {
      return NextResponse.json({ error: 'لم يتم اختيار أي ملفات' }, { status: 400 });
    }

    if (!section) {
      return NextResponse.json({ error: 'لم يتم تحديد القسم' }, { status: 400 });
    }

    const uploadedFiles = [];
    const uploadDir = path.join(process.cwd(), 'public', 'attachments', section);

    // إنشاء المجلد إذا لم يكن موجوداً
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    for (const file of files) {
      if (file.size === 0) continue;

      // إنشاء اسم ملف فريد
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const fileExtension = path.extname(file.name);
      const fileName = `${timestamp}_${randomString}${fileExtension}`;
      const filePath = path.join(uploadDir, fileName);

      // تحويل الملف إلى buffer وحفظه
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(filePath, buffer);

      uploadedFiles.push({
        originalName: file.name,
        fileName: fileName,
        filePath: `/attachments/${section}/${fileName}`,
        size: file.size,
        type: file.type,
        uploadedAt: new Date().toISOString()
      });
    }

    return NextResponse.json({
      success: true,
      message: `تم رفع ${uploadedFiles.length} ملف بنجاح`,
      files: uploadedFiles
    });

  } catch (error) {
    console.error('خطأ في رفع الملفات:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء رفع الملفات' },
      { status: 500 }
    );
  }
}

// للحصول على قائمة الملفات المرفقة
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const section = searchParams.get('section');
    const orderId = searchParams.get('orderId');

    if (!section || !orderId) {
      return NextResponse.json({ error: 'معاملات مفقودة' }, { status: 400 });
    }

    // هنا يمكن إضافة منطق للحصول على الملفات من قاعدة البيانات
    // حالياً سنعيد قائمة فارغة
    return NextResponse.json({
      success: true,
      files: []
    });

  } catch (error) {
    console.error('خطأ في جلب الملفات:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء جلب الملفات' },
      { status: 500 }
    );
  }
}
