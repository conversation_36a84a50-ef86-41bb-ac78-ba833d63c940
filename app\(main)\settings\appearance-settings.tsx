'use client';

import { useState, useRef, useEffect } from 'react';
import Image from 'next/image';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Save, ImageIcon } from 'lucide-react';
import { SystemSettings } from '@/lib/types';
import { useToast } from '@/hooks/use-toast';

export default function AppearanceSettings() {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [localSettings, setLocalSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const logoRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // جلب الإعدادات عند تحميل المكون
  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings(data);
        setLocalSettings(data);
      } else {
        throw new Error('فشل في جلب الإعدادات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في جلب الإعدادات',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setLocalSettings(prev => prev ? { ...prev, [name]: value } : null);
  };

  const handleLogoChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // رفع الملف عبر API
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/attachments', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        setLocalSettings(prev => prev ? { ...prev, logoUrl: result.url } : null);
        toast({
          title: 'تم رفع الشعار',
          description: 'تم رفع الشعار بنجاح',
        });
      } else {
        throw new Error('فشل في رفع الشعار');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في رفع الشعار',
        variant: 'destructive',
      });
    }
  };

  const handleSave = async () => {
    if (!localSettings) return;

    setIsSaving(true);
    try {
      const response = await fetch('/api/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(localSettings),
      });

      if (response.ok) {
        const result = await response.json();
        setSettings(result.data);
        toast({
          title: 'تم الحفظ',
          description: 'تم حفظ الإعدادات بنجاح',
        });
      } else {
        throw new Error('فشل في حفظ الإعدادات');
      }
    } catch (error) {
      toast({
        title: 'خطأ',
        description: 'فشل في حفظ الإعدادات',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">جاري التحميل...</div>
        </CardContent>
      </Card>
    );
  };

  if (!localSettings) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">فشل في تحميل الإعدادات</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>إعدادات المظهر والتقارير</CardTitle>
        <CardDescription>
          تُستخدم هذه البيانات لترويسة وتذييل جميع المستندات المطبوعة أو المصدَّرة.
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* شعار الشركة */}
        <div className="flex items-center gap-6">
          <Avatar className="h-24 w-24">
            <AvatarImage src={localSettings.logoUrl} alt="Logo" />
            <AvatarFallback><ImageIcon /></AvatarFallback>
          </Avatar>
          <div className="space-y-2">
            <Label>شعار الشركة</Label>
            <Input
              type="file"
              className="hidden"
              ref={logoRef}
              accept="image/*"
              onChange={handleLogoChange}
            />
            <Button
              variant="outline"
              onClick={() => logoRef.current?.click()}
            >
              تغيير الشعار
            </Button>
          </div>
        </div>

        {/* عمودان، عربى وإنجليزى */}
        <div className="grid md:grid-cols-2 gap-6">
          {/* عربي */}
          <div className="space-y-4 rtl text-right">
            <h3 className="text-lg font-semibold">البيانات العربية</h3>
            <div className="space-y-2">
              <Label>اسم الشركة (عربى)</Label>
              <Input
                name="companyNameAr"
                value={localSettings.companyNameAr}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>العنوان (عربى)</Label>
              <Input
                name="addressAr"
                value={localSettings.addressAr}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>تذييل (عربى)</Label>
              <Textarea
                name="footerTextAr"
                value={localSettings.footerTextAr}
                onChange={handleInputChange}
              />
            </div>
          </div>

          {/* English */}
          <div className="space-y-4 ltr text-left">
            <h3 className="text-lg font-semibold">English Data</h3>
            <div className="space-y-2">
              <Label>Company Name (EN)</Label>
              <Input
                name="companyNameEn"
                value={localSettings.companyNameEn}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>Address (EN)</Label>
              <Input
                name="addressEn"
                value={localSettings.addressEn}
                onChange={handleInputChange}
              />
            </div>
            <div className="space-y-2">
              <Label>Footer (EN)</Label>
              <Textarea
                name="footerTextEn"
                value={localSettings.footerTextEn}
                onChange={handleInputChange}
              />
            </div>
          </div>
        </div>

        {/* معلومات اتصال مشتركة */}
        <div className="grid md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <Label>رقم الهاتف</Label>
            <Input
              name="phone"
              value={localSettings.phone}
              onChange={handleInputChange}
            />
          </div>
          <div className="space-y-2">
            <Label>البريد الإلكترونى</Label>
            <Input
              name="email"
              type="email"
              value={localSettings.email}
              onChange={handleInputChange}
            />
          </div>
          <div className="space-y-2">
            <Label>الموقع</Label>
            <Input
              name="website"
              value={localSettings.website}
              onChange={handleInputChange}
            />
          </div>
        </div>
      </CardContent>

      <CardFooter className="justify-end">
        <Button onClick={handleSave} disabled={isSaving}>
          <Save className="ml-2 h-4 w-4" />
          {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
        </Button>
      </CardFooter>
    </Card>
  );
}