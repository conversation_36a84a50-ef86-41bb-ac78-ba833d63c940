import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

export async function DELETE(request: NextRequest) {
  try {
    const { fileName } = await request.json();
    
    if (!fileName) {
      return NextResponse.json({ error: 'اسم الملف مطلوب' }, { status: 400 });
    }

    const filePath = path.join(ATTACHMENTS_DIR, fileName);
    
    if (existsSync(filePath)) {
      await unlink(filePath);
      return NextResponse.json({ success: true, message: 'تم حذف الملف بنجاح' });
    } else {
      return NextResponse.json({ error: 'الملف غير موجود' }, { status: 404 });
    }

  } catch (error) {
    console.error('خطأ في حذف الملف:', error);
    return NextResponse.json({ error: 'فشل في حذف الملف' }, { status: 500 });
  }
}
