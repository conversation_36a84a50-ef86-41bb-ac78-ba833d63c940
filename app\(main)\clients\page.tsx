'use client';

import { useState } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Contact } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Edit, Trash2 } from 'lucide-react';

type EntryType = 'client' | 'supplier';

const initialFormState: Omit<Contact, 'id'> = {
  name: '',
  phone: '',
  email: '',
};

export default function ClientsPage() {
  const {
    clients,
    suppliers,
    addContact,
    updateContact,
    deleteContact,
    checkClientRelations,
    checkSupplierRelations,
    currentUser,
  } = useStore();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogType, setDialogType] = useState<EntryType>('client');
  const [editingEntry, setEditingEntry] = useState<Contact | null>(null);
  const [entryToDelete, setEntryToDelete] = useState<{
    entry: Contact;
    type: EntryType;
  } | null>(null);
  const [formData, setFormData] =
    useState<Omit<Contact, 'id'>>(initialFormState);

  const permissions = currentUser?.permissions.clients;

  const handleOpenDialog = (type: EntryType, entry: Contact | null) => {
    setDialogType(type);
    if (entry) {
      setEditingEntry(entry);
      setFormData(entry);
    } else {
      setEditingEntry(null);
      setFormData(initialFormState);
    }
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingEntry(null);
    setFormData(initialFormState);
  };

  const handleSave = () => {
    const typeName = dialogType === 'client' ? 'العميل' : 'المورد';
    if (editingEntry) {
      updateContact({ ...formData, id: editingEntry.id }, dialogType);
      toast({
        title: 'تم التحديث',
        description: `تم تحديث بيانات ${typeName} بنجاح.`,
      });
    } else {
      addContact(formData, dialogType);
      toast({
        title: 'تمت الإضافة',
        description: `تمت إضافة ${typeName} بنجاح.`,
      });
    }
    handleDialogClose();
  };

  const handleDelete = () => {
    if (entryToDelete) {
      const typeName = entryToDelete.type === 'client' ? 'العميل' : 'المورد';

      try {
        // فحص العلاقات قبل الحذف
        const relationCheck = entryToDelete.type === 'client'
          ? checkClientRelations(entryToDelete.entry.id)
          : checkSupplierRelations(entryToDelete.entry.id);

        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setEntryToDelete(null);
          return;
        }

        deleteContact(entryToDelete.entry.id, entryToDelete.type);
        toast({
          title: 'تم الحذف',
          description: `تم حذف ${typeName} بنجاح.`,
          variant: 'destructive',
        });
        setEntryToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setEntryToDelete(null);
      }
    }
  };

  const renderTable = (data: Contact[], type: EntryType) => (
    <div className="rounded-lg border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>الاسم</TableHead>
            <TableHead>رقم الهاتف</TableHead>
            <TableHead>البريد الإلكتروني</TableHead>
            <TableHead>إجراءات</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((entry) => (
            <TableRow key={entry.id}>
              <TableCell className="font-medium">{entry.name}</TableCell>
              <TableCell>{entry.phone}</TableCell>
              <TableCell>{entry.email}</TableCell>
              <TableCell>
                <div className="flex gap-2">
                  {permissions?.edit && (
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleOpenDialog(type, entry)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  )}
                  {permissions?.delete && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:text-destructive"
                      onClick={() => setEntryToDelete({ entry, type })}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );

  return (
    <>
      <Tabs defaultValue="clients" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="clients">العملاء</TabsTrigger>
          <TabsTrigger value="suppliers">الموردين</TabsTrigger>
        </TabsList>
        <TabsContent value="clients">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">قائمة العملاء</h2>
              {permissions?.create && (
                <Button onClick={() => handleOpenDialog('client', null)}>
                  إضافة عميل جديد
                </Button>
              )}
            </div>
            {renderTable(clients, 'client')}
          </div>
        </TabsContent>
        <TabsContent value="suppliers">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">قائمة الموردين</h2>
              {permissions?.create && (
                <Button onClick={() => handleOpenDialog('supplier', null)}>
                  إضافة مورد جديد
                </Button>
              )}
            </div>
            {renderTable(suppliers, 'supplier')}
          </div>
        </TabsContent>
      </Tabs>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={(e) => e.preventDefault()}
          onCloseAutoFocus={handleDialogClose}
        >
          <DialogHeader>
            <DialogTitle>
              إضافة {dialogType === 'client' ? 'عميل' : 'مورد'} جديد
            </DialogTitle>
            <DialogDescription>
              أدخل بيانات {dialogType === 'client' ? 'العميل' : 'المورد'} الجديد
              هنا.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                className="col-span-3"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="phone" className="text-right">
                الهاتف
              </Label>
              <Input
                id="phone"
                className="col-span-3"
                value={formData.phone}
                onChange={(e) =>
                  setFormData({ ...formData, phone: e.target.value })
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                البريد الإلكتروني
              </Label>
              <Input
                id="email"
                className="col-span-3"
                value={formData.email}
                onChange={(e) =>
                  setFormData({ ...formData, email: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!entryToDelete}
        onOpenChange={() => setEntryToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              ⚠️ تأكيد حذف {entryToDelete?.type === 'client' ? 'العميل' : 'المورد'}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div className="text-gray-700">
                  هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف بيانات
                  <span className="font-semibold text-red-600">
                    {entryToDelete?.type === 'client' ? ' العميل ' : ' المورد '}
                    "{entryToDelete?.entry.name}"
                  </span>
                  بشكل دائم.
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
                  <div className="text-yellow-800 text-sm">
                    <strong>تنبيه:</strong> سيتم فحص العلاقات المرتبطة قبل الحذف.
                    {entryToDelete?.type === 'client'
                      ? ' إذا كان للعميل مبيعات أو مرتجعات، فلن يتم الحذف.'
                      : ' إذا كان للمورد أوامر توريد، فلن يتم الحذف.'
                    }
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200">
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
