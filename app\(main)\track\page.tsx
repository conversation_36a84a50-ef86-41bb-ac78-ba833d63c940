'use client';

import { useState, useEffect, useMemo } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useStore } from '@/context/store';
import { ar } from 'date-fns/locale';
import './track.css';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Barcode,
  Package,
  Wrench,
  ShoppingCart,
  Undo2,
  ClipboardCheck,
  Shuffle,
  User,
  PackageCheck,
  ShieldCheck,
  FileText,
  Replace,
  Printer,
  FileDown,
} from 'lucide-react';
import {
  addDays,
  addMonths,
  addYears,
  isAfter,
  formatDistanceToNowStrict,
  format,
} from 'date-fns';
import { exportDataToPDF, exportHTMLToPDF, exportDeviceTrackingReport, exportDeviceTrackingReportDirect, printElement, printDeviceData } from '@/lib/export-utils/html-to-pdf';
import { createArabicPDFWithCanvas } from '@/lib/export-utils/canvas-pdf';
import { printDeviceTrackingReport, printElementWithSettings } from '@/lib/device-tracking-utils';
import ReportPreview from '@/components/ReportPreview';
import './print-styles.css';

// دالة مساعدة لتنسيق التاريخ بالعربية
function formatArabicDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

type TimelineEvent = {
  icon: React.ReactNode;
  title: string;
  description: string;
  date: string; // ISO string for sorting
  color: string;
  user?: string;
  formattedDate?: string;
};

export default function TrackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const {
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
  } = useStore();

  const [imei, setImei] = useState('');
  const [searchedImei, setSearchedImei] = useState('');
  const [isCustomerView, setIsCustomerView] = useState(false);
  const [showReportPreview, setShowReportPreview] = useState(false);

  useEffect(() => {
    const idFromQuery = searchParams.get('id');
    if (idFromQuery) {
      setImei(idFromQuery);
      setSearchedImei(idFromQuery);
    }
  }, [searchParams]);

  const handleSearch = () => {
    if (!imei) return;
    setSearchedImei(imei);
    const params = new URLSearchParams(searchParams);
    params.set('id', imei);
    router.replace(`/track?${params.toString()}`);
  };

  const fullTimelineEvents = useMemo((): TimelineEvent[] => {
    if (!searchedImei) return [];

    const events: TimelineEvent[] = [];
    const device = devices.find((d) => d.id === searchedImei);

    // 1. Supply Event
    const supplyOrder = supplyOrders.find((so) =>
      so.items.some((item) => item.imei === searchedImei),
    );
    if (supplyOrder) {
      const supplier = suppliers.find((s) => s.id === supplyOrder.supplierId);
      events.push({
        icon: <Package className="h-5 w-5" />,
        title: 'توريد',
        description: `تم استلام الجهاز من المورد '${supplier?.name || 'غير معروف'}' ضمن أمر التوريد ${supplyOrder.supplyOrderId}.`,
        date: supplyOrder.supplyDate,
        color: 'bg-cyan-500/20 text-cyan-400',
        user: supplyOrder.employeeName,
      });
    }

    // 2. Evaluation Events
    evaluationOrders.forEach((order) => {
      const evaluatedItem = order.items.find(
        (item) => item.deviceId === searchedImei,
      );
      if (evaluatedItem) {
        events.push({
          icon: <ClipboardCheck className="h-5 w-5" />,
          title: 'فحص وتقييم',
          description: `تم فحص الجهاز. النتيجة: ${evaluatedItem.finalGrade}. ${evaluatedItem.fault || evaluatedItem.damageType || ''}`,
          date: order.date,
          color: 'bg-indigo-500/20 text-indigo-400',
          user: order.employeeName,
        });
      }
    });

    // 3. Maintenance Events
    maintenanceHistory.forEach((log) => {
      if (log.deviceId === searchedImei) {
        events.push({
          icon: <Wrench className="h-5 w-5" />,
          title: 'إتمام الصيانة',
          description: `تمت معالجة الجهاز في الصيانة. النتيجة: ${log.result}. ملاحظات: ${log.notes || 'لا يوجد'}.`,
          date: log.repairDate,
          color: 'bg-yellow-500/20 text-yellow-400',
          user: 'قسم الصيانة', // Assuming a generic user for now
        });
        if (log.status === 'acknowledged' && log.acknowledgedDate) {
          events.push({
            icon: <PackageCheck className="h-5 w-5" />,
            title: 'استلام في المخزن',
            description: `تم استلام الجهاز في مخزن '${log.warehouseName || 'غير معروف'}'.`,
            date: log.acknowledgedDate,
            color: 'bg-purple-500/20 text-purple-400',
            user: log.acknowledgedBy,
          });
        }
      }
    });

    // 4. Warehouse Transfer Events
    warehouseTransfers.forEach((transfer) => {
      if (transfer.items.some((item) => item.deviceId === searchedImei)) {
        const statusText = transfer.status === 'completed' ? 'مكتمل' : 'معلق';
        events.push({
          icon: <Shuffle className="h-5 w-5" />,
          title: `تحويل مخزني (${statusText})`,
          description: `تم نقل الجهاز من '${transfer.fromWarehouseName}' إلى '${transfer.toWarehouseName}'. أمر التحويل: ${transfer.transferNumber}.`,
          date: transfer.date,
          color: 'bg-gray-500/20 text-gray-400',
          user: transfer.employeeName,
        });
      }
    });

    // 5. Sale Event
    const sale = sales.find((s) =>
      s.items.some((item) => item.deviceId === searchedImei),
    );
    if (sale) {
      events.push({
        icon: <ShoppingCart className="h-5 w-5" />,
        title: 'بيع',
        description: `تم بيع الجهاز للعميل '${sale.clientName}' ضمن فاتورة ${sale.soNumber}.`,
        date: sale.date,
        color: 'bg-green-500/20 text-green-400',
        user: 'قسم المبيعات', // Placeholder user
      });
    }

    // 6. Return and Replacement Events
    returns.forEach((returnOrder) => {
      const returnedItem = returnOrder.items.find(
        (item) => item.deviceId === searchedImei,
      );
      const replacementItem = returnOrder.items.find(
        (item) => item.replacementDeviceId === searchedImei,
      );

      if (returnedItem) {
        events.push({
          icon: <Undo2 className="h-5 w-5" />,
          title: 'إرجاع جهاز',
          description: `تم إرجاع هذا الجهاز من العميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber}. سبب الإرجاع: ${returnedItem.returnReason}.`,
          date: returnOrder.date,
          color: 'bg-red-500/20 text-red-400',
          user: 'قسم المرتجعات',
        });
      }

      if (replacementItem) {
        events.push({
          icon: <Replace className="h-5 w-5" />,
          title: 'جهاز بديل',
          description: `تم صرف هذا الجهاز كبديل للعميل '${returnOrder.clientName}' في أمر المرتجع رقم ${returnOrder.roNumber} عن الجهاز الأصلي (${replacementItem.deviceId}).`,
          date: returnOrder.date, // The replacement happens on the same return date
          color: 'bg-blue-500/20 text-blue-400',
          user: 'قسم المرتجعات',
        });
      }
    });

    return events
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .map((event) => ({
        ...event,
        formattedDate: formatArabicDate(event.date),
      }));
  }, [
    searchedImei,
    devices,
    sales,
    returns,
    supplyOrders,
    suppliers,
    evaluationOrders,
    maintenanceHistory,
    warehouseTransfers,
  ]);

  const customerViewDetails = useMemo(() => {
    if (!searchedImei) return null;

    const device = devices.find((d) => d.id === searchedImei);
    if (!device) return null;

    const lastSale = sales
      .filter((s) => s.items.some((item) => item.deviceId === searchedImei))
      .sort(
        (a, b) => new Date(b.date).getTime() - new Date(a.date).getTime(),
      )[0];

    let warrantyInfo = {
      status: 'لا يوجد بيع مسجل',
      expiryDate: null,
      remaining: null,
    };

    if (lastSale) {
      const saleDate = new Date(lastSale.date);
      let expiryDate: Date | null = null;

      switch (lastSale.warrantyPeriod) {
        case '3d':
          expiryDate = addDays(saleDate, 3);
          break;
        case '1w':
          expiryDate = addDays(saleDate, 7);
          break;
        case '1m':
          expiryDate = addMonths(saleDate, 1);
          break;
        case '3m':
          expiryDate = addMonths(saleDate, 3);
          break;
        case '6m':
          expiryDate = addMonths(saleDate, 6);
          break;
        case '1y':
          expiryDate = addYears(saleDate, 1);
          break;
        default:
          break;
      }

      if (expiryDate) {
        const today = new Date();
        if (isAfter(expiryDate, today)) {
          warrantyInfo.status = 'في الضمان';
          warrantyInfo.remaining = formatDistanceToNowStrict(expiryDate, {
            locale: ar,
            addSuffix: true,
          });
        } else {
          warrantyInfo.status = 'ضمان منتهي';
        }
        warrantyInfo.expiryDate = format(expiryDate, 'yyyy-MM-dd');
      } else {
        warrantyInfo.status = 'بدون ضمان';
      }
    }

    const replacementInfo = returns.find((r) =>
      r.items.some((item) => item.replacementDeviceId === searchedImei),
    );
    const originalItem = replacementInfo?.items.find(
      (item) => item.replacementDeviceId === searchedImei,
    );

    return {
      device,
      lastSale,
      warrantyInfo,
      originalItemInfo: originalItem
        ? {
            ...originalItem,
            returnDate: replacementInfo.date,
          }
        : null,
    };
  }, [searchedImei, devices, sales, returns]);

  const device = devices.find((d) => d.id === searchedImei);

  const handlePrint = async (action: 'print' | 'download') => {
    if (!searchedImei || !device) return;

    // تحضير البيانات للطباعة
    const deviceData = {
      model: device.model,
      id: searchedImei,
      status: device.status,
      lastSale: isCustomerView && customerViewDetails?.lastSale ? {
        clientName: customerViewDetails.lastSale.clientName,
        soNumber: customerViewDetails.lastSale.soNumber,
        opNumber: customerViewDetails.lastSale.opNumber,
        date: customerViewDetails.lastSale.date
      } : undefined,
      warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
        status: customerViewDetails.warrantyInfo.status,
        expiryDate: customerViewDetails.warrantyInfo.expiryDate,
        remaining: customerViewDetails.warrantyInfo.remaining
      } : undefined
    };

    try {
      // استخدام الدالة المحدثة للطباعة
      await printDeviceTrackingReport(deviceData, fullTimelineEvents, {
        language: 'both',
        isCustomerView,
        action,
        filename: `${isCustomerView ? 'customer_' : ''}device_report_${searchedImei}.pdf`
      });
    } catch (error) {
      console.error('Error printing device report:', error);

      // العودة للطريقة القديمة في حالة الخطأ
      if (action === 'print') {
        const elementId = isCustomerView ? 'customer-view-container' : 'timeline-container';
        const title = isCustomerView
          ? `تقرير تتبع الجهاز (نسخة العميل)`
          : `سجل تاريخ الجهاز - ${device.model} (${searchedImei})`;

        printElementWithSettings(elementId, title, {
          language: 'both',
          isCustomerView,
          action
        });
      }
    }

  };

  return (
    <div className="flex flex-col gap-6" dir="rtl">
      <Card>
        <CardHeader>
          <CardTitle>تتبع الاجهزة</CardTitle>
          <CardDescription>
            أدخل الرقم التسلسلي (IMEI) لعرض سجل تاريخ الجهاز.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex w-full max-w-lg items-center space-x-2 space-x-reverse">
            <Input
              type="text"
              placeholder="أدخل IMEI أو امسح الباركود"
              className="text-right"
              value={imei}
              onChange={(e) => setImei(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch}>
              <Barcode className="ml-2 h-4 w-4" />
              بحث
            </Button>
          </div>
          <div className="mt-4 flex items-center space-x-2 space-x-reverse">
            <Checkbox
              id="customer-view"
              checked={isCustomerView}
              onCheckedChange={(checked) => setIsCustomerView(!!checked)}
            />
            <Label htmlFor="customer-view" className="cursor-pointer">
              نسخة العميل
            </Label>
          </div>
        </CardContent>
      </Card>

      {searchedImei && !device && (
        <Card>
          <CardHeader>
            <CardTitle>نتيجة البحث</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-destructive">الجهاز غير موجود في النظام.</p>
          </CardContent>
        </Card>
      )}

      {searchedImei &&
        device &&
        (isCustomerView ? (
          customerViewDetails && (
            <div className="space-y-4">
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowReportPreview(true)}>
                  <FileText className="ml-2 h-4 w-4" /> معاينة وطباعة التقرير
                </Button>
                <Button variant="outline" onClick={() => handlePrint('print')}>
                  <Printer className="ml-2 h-4 w-4" /> طباعة سريعة
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handlePrint('download')}
                >
                  <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                </Button>
              </div>
              <div id="customer-view-container" className="grid grid-cols-1 md:grid-cols-2 gap-6 animate-in fade-in-50 print-section">
                <Card className="md:col-span-2">
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <FileText className="h-5 w-5 text-primary" />
                      <CardTitle>تفاصيل البيع</CardTitle>
                    </div>
                    <CardDescription className="rtl-container">
                      آخر عملية بيع مسجلة لهذا الجهاز.
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    {customerViewDetails.lastSale ? (
                      <>
                        <p>
                          <strong>العميل:</strong>{' '}
                          {customerViewDetails.lastSale.clientName}
                        </p>
                        <p>
                          <strong>فاتورة البيع:</strong>{' '}
                          {customerViewDetails.lastSale.soNumber}
                        </p>
                        <p>
                          <strong>الفاتورة الرسمية:</strong>{' '}
                          {customerViewDetails.lastSale.opNumber || 'لا يوجد'}
                        </p>
                        <p>
                          <strong>تاريخ البيع:</strong>{' '}
                          {formatArabicDate(customerViewDetails.lastSale.date)}
                        </p>
                      </>
                    ) : (
                      <p className="text-muted-foreground">
                        لا توجد عملية بيع مسجلة لهذا الجهاز.
                      </p>
                    )}
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <div className="flex items-center gap-2">
                      <ShieldCheck className="h-5 w-5 text-primary" />
                      <CardTitle>حالة الضمان</CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-2 warranty-info">
                    <p>
                      <strong>الحالة:</strong>{' '}
                      {customerViewDetails.warrantyInfo.status}
                    </p>
                    {customerViewDetails.warrantyInfo.expiryDate && (
                      <p>
                        <strong>تاريخ الانتهاء:</strong>{' '}
                        {customerViewDetails.warrantyInfo.expiryDate}
                      </p>
                    )}
                    {customerViewDetails.warrantyInfo.remaining && (
                      <p>
                        <strong>الوقت المتبقي:</strong>{' '}
                        {customerViewDetails.warrantyInfo.remaining}
                      </p>
                    )}
                  </CardContent>
                </Card>
                {customerViewDetails.originalItemInfo && (
                  <Card>
                    <CardHeader>
                      <div className="flex items-center gap-2">
                        <Replace className="h-5 w-5 text-primary" />
                        <CardTitle>معلومات الاستبدال</CardTitle>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <p>هذا الجهاز تم تسليمه كبديل لجهاز آخر.</p>
                      <p>
                        <strong>الجهاز الأصلي:</strong>{' '}
                        {customerViewDetails.originalItemInfo.model}
                      </p>
                      <p>
                        <strong>الرقم التسلسلي الأصلي:</strong>{' '}
                        {customerViewDetails.originalItemInfo.deviceId}
                      </p>
                      <p>
                        <strong>تاريخ الإرجاع:</strong>{' '}
                        {formatArabicDate(customerViewDetails.originalItemInfo.returnDate)}
                      </p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          )
        ) : (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle>سجل تاريخ الجهاز: {searchedImei}</CardTitle>
                  <CardDescription>
                    {device.model} - الحالة الحالية:{' '}
                    <span className="font-semibold">{device.status}</span>
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowReportPreview(true)}
                    title="معاينة وطباعة التقرير"
                  >
                    <FileText className="h-4 w-4 ml-1" />
                    معاينة التقرير
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePrint('print')}
                    title="طباعة سريعة"
                  >
                    <Printer className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handlePrint('download')}
                    title="تصدير PDF"
                  >
                    <FileDown className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {fullTimelineEvents.length > 0 ? (
                <div id="timeline-container" className="relative pl-8 print-section">
                  <div className="absolute left-4 top-0 h-full w-0.5 bg-border"></div>
                  {fullTimelineEvents.map((event, index) => (
                    <div key={index} className="relative mb-8 flex items-start timeline-event">
                      <div
                        className={`z-10 flex h-9 w-9 items-center justify-center rounded-full ${event.color}`}
                      >
                        {event.icon}
                      </div>
                      <div className="pr-8">
                        <div className="flex items-center gap-4">
                          <h4 className="font-semibold timeline-title">{event.title}</h4>
                          <time className="text-xs text-muted-foreground/80 timeline-date">
                            {event.formattedDate}
                          </time>
                        </div>
                        <p className="text-sm text-muted-foreground timeline-description">
                          {event.description}
                        </p>
                        {event.user && (
                          <div className="mt-1 flex items-center gap-1.5 text-xs text-muted-foreground/80 timeline-user">
                            <User className="h-3 w-3" />
                            <span>بواسطة: {event.user}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">
                  لا يوجد سجل تاريخ مسجل لهذا الجهاز.
                </p>
              )}
            </CardContent>
          </Card>
        ))}

      {/* مكون معاينة التقرير */}
      {searchedImei && device && (
        <ReportPreview
          isOpen={showReportPreview}
          onClose={() => setShowReportPreview(false)}
          deviceData={{
            model: device.model,
            id: searchedImei,
            status: device.status,
            lastSale: isCustomerView && customerViewDetails?.lastSale ? {
              clientName: customerViewDetails.lastSale.clientName,
              soNumber: customerViewDetails.lastSale.soNumber,
              opNumber: customerViewDetails.lastSale.opNumber,
              date: customerViewDetails.lastSale.date
            } : undefined,
            warrantyInfo: isCustomerView && customerViewDetails?.warrantyInfo ? {
              status: customerViewDetails.warrantyInfo.status,
              expiryDate: customerViewDetails.warrantyInfo.expiryDate,
              remaining: customerViewDetails.warrantyInfo.remaining
            } : undefined
          }}
          timelineEvents={fullTimelineEvents.map(event => ({
            id: `${event.date}-${event.title}`,
            type: event.title.includes('بيع') ? 'بيع' :
                  event.title.includes('إرجاع') ? 'إرجاع' :
                  event.title.includes('صيانة') ? 'صيانة' :
                  event.title.includes('تقييم') ? 'تقييم' :
                  event.title.includes('توريد') ? 'توريد' : 'عام',
            title: event.title,
            description: event.description,
            date: event.date,
            user: event.user
          }))}
        />
      )}
    </div>
  );
}
