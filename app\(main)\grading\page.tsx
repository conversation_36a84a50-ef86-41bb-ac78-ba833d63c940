'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type {
  EvaluatedDevice,
  EvaluationGrade,
  ScreenGrade,
  NetworkGrade,
  FinalGrade,
  FaultType,
  DamageType,
  EvaluationOrder,
  SystemSettings,
} from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Plus<PERSON>ircle,
  Trash2,
  Save,
  FileDown,
  Printer,
  X,
  Upload,
  FileSpreadsheet,
  FolderOpen,
  Trash,
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';

const initialFormState = {
  imei: '',
  externalGrade: 'بدون' as EvaluationGrade,
  screenGrade: 'بدون' as ScreenGrade,
  networkGrade: 'مفتوح رسمي' as NetworkGrade,
  finalGrade: 'جاهز للبيع' as FinalGrade,
  fault: '' as FaultType | '',
  customFault: '',
  damage: '' as DamageType | '',
  customDamage: '',
};

export default function GradingPage() {
  const {
    devices,
    addEvaluationOrder,
    evaluationOrders,
    deleteEvaluationOrder,
    updateEvaluationOrder,
    systemSettings,
    currentUser,
  } = useStore();
  const { toast } = useToast();

  // 🔐 نظام الصلاحيات
  const canView = currentUser?.permissions?.grading?.view ?? false;
  const canCreate = currentUser?.permissions?.grading?.create ?? false;
  const canEdit = currentUser?.permissions?.grading?.edit ?? false;
  const canDelete = currentUser?.permissions?.grading?.delete ?? false;

  // منع الوصول للصفحة بدون صلاحية العرض
  if (!canView) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-600">غير مصرح لك بالوصول</h2>
          <p className="text-gray-500 mt-2">ليس لديك صلاحية لعرض صفحة التقييم</p>
        </div>
      </div>
    );
  }

  // 📝 وضع الإنشاء (Create Mode)
  const [isCreating, setIsCreating] = useState(false);

  const [evaluationId, setEvaluationId] = useState('');
  const [imei, setImei] = useState('');
  const [currentDevice, setCurrentDevice] = useState<{
    id: string;
    model: string;
  } | null>(null);
  const [formState, setFormState] = useState(initialFormState);
  const [evaluatedItems, setEvaluatedItems] = useState<EvaluatedDevice[]>([]);
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [generalNotes, setGeneralNotes] = useState('');
  const [previousEvaluations, setPreviousEvaluations] = useState<
    EvaluationOrder[]
  >([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isLoadOrderDialogOpen, setIsLoadOrderDialogOpen] = useState(false);
  const [loadedOrder, setLoadedOrder] = useState<EvaluationOrder | null>(null);
  const [orderToDelete, setOrderToDelete] = useState<EvaluationOrder | null>(
    null,
  );

  // دالة تنسيق التاريخ والوقت
  const formatDateTime = (dateTimeString: string): string => {
    if (!dateTimeString) return '';

    try {
      const date = new Date(dateTimeString);
      if (isNaN(date.getTime())) return dateTimeString;

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}`;
    } catch (error) {
      return dateTimeString;
    }
  };

  useEffect(() => {
    if (loadedOrder) {
      setEvaluationId(loadedOrder.orderId);
      return;
    }
    if (!evaluationOrders) return;
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    const todayOrders = evaluationOrders.filter((order) =>
      order.orderId.startsWith(`EVAL-${today}`),
    );
    const nextId = todayOrders.length + 1;
    const formattedId = `EVAL-${today}-${String(nextId).padStart(3, '0')}`;
    setEvaluationId(formattedId);
  }, [evaluationOrders, loadedOrder]);

  const handleImeiSearch = () => {
    if (!imei) return;

    const device = devices.find((d) => d.id === imei);
    if (!device) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'الجهاز غير موجود في النظام.',
      });
      setCurrentDevice(null);
      return;
    }
    if (device.status === 'مباع') {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'لا يمكن تقييم جهاز تم بيعه.',
      });
      setCurrentDevice(null);
      return;
    }
    if (evaluatedItems.some((item) => item.deviceId === imei)) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'تم تقييم هذا الجهاز بالفعل في هذه الجلسة.',
      });
      return;
    }

    const prevEvals = evaluationOrders.filter((order) =>
      order.items.some((item) => item.deviceId === imei),
    );
    setPreviousEvaluations(prevEvals);
    setCurrentDevice({ id: device.id, model: device.model });
    toast({
      title: 'تم العثور على الجهاز',
      description: `جاهز لتقييم ${device.model}`,
    });
  };

  const handleAddToList = () => {
    if (!currentDevice) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى البحث عن جهاز أولاً.',
      });
      return;
    }

    const newItem: EvaluatedDevice = {
      deviceId: currentDevice.id,
      model: currentDevice.model,
      externalGrade: formState.externalGrade,
      screenGrade: formState.screenGrade,
      networkGrade: formState.networkGrade,
      finalGrade: formState.finalGrade,
      fault:
        formState.finalGrade === 'يحتاج صيانة' ||
        formState.finalGrade === 'عيب فني'
          ? formState.fault === 'أعطال أخرى'
            ? formState.customFault
            : formState.fault
          : undefined,
      damageType:
        formState.finalGrade === 'تالف'
          ? formState.damage === 'أخرى'
            ? formState.customDamage
            : formState.damage
          : undefined,
    };

    setEvaluatedItems((prev) => [...prev, newItem]);
    setCurrentDevice(null);
    setImei('');
    setFormState(initialFormState);
    setPreviousEvaluations([]);
    toast({
      title: 'تمت الإضافة',
      description: `تمت إضافة ${currentDevice.model} إلى قائمة التقييم.`,
    });
  };

  const handleRemoveItem = (deviceId: string) => {
    setEvaluatedItems((prev) =>
      prev.filter((item) => item.deviceId !== deviceId),
    );
  };

  // دوال وضع الإنشاء
  const startCreating = () => {
    resetPage();
    setIsCreating(true);
    toast({
      title: 'وضع الإنشاء',
      description: 'تم تفعيل وضع إنشاء أمر تقييم جديد',
    });
  };

  const resetPage = () => {
    setImei('');
    setCurrentDevice(null);
    setFormState(initialFormState);
    setEvaluatedItems([]);
    setGeneralNotes('');
    setPreviousEvaluations([]);
    setIsCancelAlertOpen(false);
    setLoadedOrder(null);
    setIsCreating(false); // العودة إلى وضع القراءة فقط
  };

  const handleCreateNew = () => {
    startCreating(); // استخدام دالة وضع الإنشاء الجديدة
  };

  const handleLoadOrder = (order: EvaluationOrder) => {
    setLoadedOrder(order);
    setEvaluationId(order.orderId);
    setEvaluatedItems(order.items);
    setGeneralNotes(order.notes || '');
    setImei('');
    setCurrentDevice(null);
    setFormState(initialFormState);
    setPreviousEvaluations([]);
    setIsLoadOrderDialogOpen(false);
    setIsCreating(false); // تعطيل وضع الإنشاء عند تحميل أمر موجود
    toast({
      title: 'تم التحميل',
      description: `تم تحميل الأمر ${order.orderId}`,
    });
  };

  const handleDeleteOrder = () => {
    if (orderToDelete) {
      try {
        const relationCheck = store.checkEvaluationOrderRelations(orderToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setOrderToDelete(null);
          return;
        }

        deleteEvaluationOrder(orderToDelete.id);
        toast({
          variant: 'destructive',
          title: 'تم الحذف',
          description: `تم حذف الأمر ${orderToDelete.orderId}`,
        });
        resetPage();
        setOrderToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setOrderToDelete(null);
      }
    }
  };

  const handleSaveEvaluation = () => {
    if (evaluatedItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'القائمة فارغة',
        description: 'يرجى تقييم جهاز واحد على الأقل.',
      });
      return;
    }

    const orderData = {
      orderId: evaluationId,
      employeeName: 'مدير النظام',
      date: new Date().toISOString(),
      items: evaluatedItems,
      notes: generalNotes,
    };

    if (loadedOrder) {
      updateEvaluationOrder({
        ...orderData,
        id: loadedOrder.id,
        createdAt: loadedOrder.createdAt // الحفاظ على التاريخ الأصلي
      });
      toast({
        title: 'تم التحديث',
        description: `تم تحديث أمر التقييم ${loadedOrder.orderId} بنجاح.`,
      });
    } else {
      addEvaluationOrder(orderData);
      toast({
        title: 'تم الحفظ',
        description: `تم حفظ أمر التقييم ${evaluationId} بنجاح.`,
      });
    }

    resetPage();
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    const newItems: EvaluatedDevice[] = [];
    const existingDeviceIds = new Set(
      evaluatedItems.map((item) => item.deviceId),
    );

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }

      const device = devices.find((d) => d.id === imei);
      if (!device || device.status === 'مباع') {
        invalidCount++;
        continue;
      }

      const newItem: EvaluatedDevice = {
        deviceId: device.id,
        model: device.model,
        externalGrade: 'بدون',
        screenGrade: 'بدون',
        networkGrade: 'مفتوح رسمي',
        finalGrade: 'جاهز للبيع',
      };
      newItems.push(newItem);
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setEvaluatedItems((prev) => [...prev, ...newItems]);
    }

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${addedCount} جهازًا. تم تخطي ${invalidCount} جهازًا (غير صالح) و ${duplicateCount} جهازًا (مكرر).`,
    });

    if (event.target) event.target.value = '';
  };

  const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
    const addHeader = () => {
      if (settings.logoUrl) {
        try {
          doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
        } catch (e) {
          console.error('Error adding logo image to PDF:', e);
        }
      }
      doc.setFontSize(16);
      doc.text(settings.companyName, 190, 15, { align: 'right' });
      doc.setFontSize(10);
      doc.text(settings.companyAddress, 190, 22, { align: 'right' });
      doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
      doc.setLineWidth(0.5);
      doc.line(15, 35, 195, 35);
    };

    const addFooter = (data: any) => {
      const pageCount = doc.internal.pages.length;
      doc.setFontSize(8);
      doc.text(
        `صفحة ${data.pageNumber} من ${pageCount - 1}`,
        data.settings.margin.left,
        doc.internal.pageSize.height - 10,
      );
      if (settings.reportFooter) {
        doc.text(
          settings.reportFooter,
          195,
          doc.internal.pageSize.height - 10,
          { align: 'right' },
        );
      }
    };

    return { addHeader, addFooter };
  };

  const handleExport = async (action: 'print' | 'download') => {
    if (evaluatedItems.length === 0) return;
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);

    addHeader();

    doc.setFontSize(18);
    doc.text('تقرير الفحص والتقييم', 190, 45, { align: 'right' });

    doc.setFontSize(12);
    doc.text(`رقم الأمر: ${evaluationId}`, 190, 52, { align: 'right' });
    doc.text(`التاريخ: ${new Date().toLocaleDateString('ar-EG')}`, 190, 59, {
      align: 'right',
    });

    autoTable(doc, {
      startY: 65,
      head: [['الوصف', 'التقييم النهائي', 'الموديل', 'الرقم التسلسلي']],
      body: evaluatedItems.map((item) => [
        item.fault || item.damageType || 'N/A',
        item.finalGrade,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      didDrawPage: addFooter,
      theme: 'grid',
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`${evaluationId}.pdf`);
    }
  };

  const handleExportExcel = async () => {
    if (evaluatedItems.length === 0) return;
    const XLSX = await import('xlsx');
    const worksheet = XLSX.utils.json_to_sheet(
      evaluatedItems.map((item) => ({
        'رقم الأمر': evaluationId,
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
        'التقييم الخارجي': item.externalGrade,
        'تقييم الشاشة': item.screenGrade,
        الشبكة: item.networkGrade,
        'التقييم النهائي': item.finalGrade,
        'العطل/التلف': item.fault || item.damageType,
      })),
    );
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'تقرير التقييم');
    XLSX.writeFile(workbook, `Evaluation-${evaluationId}.xlsx`);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <h1 className="text-2xl font-bold">
          {loadedOrder
            ? `تعديل الأمر ${loadedOrder.orderId}`
            : 'صفحة الفحص والتقييم'}
        </h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsLoadOrderDialogOpen(true)}
            className="border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
          >
            <FolderOpen className="ml-2 h-4 w-4" /> تحميل أمر سابق
          </Button>
          {canCreate && (
            <Button
              onClick={handleCreateNew}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <PlusCircle className="ml-2 h-4 w-4" /> إنشاء أمر جديد
            </Button>
          )}
          {canDelete && (
            <Button
              variant="destructive"
              onClick={() => setOrderToDelete(loadedOrder)}
              disabled={!loadedOrder}
              className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 disabled:from-gray-300 disabled:to-gray-400 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:transform-none"
            >
              <Trash className="ml-2 h-4 w-4" /> حذف الأمر
            </Button>
          )}
        </div>

        {/* رسالة توضيحية في وضع القراءة */}
        {!isCreating && !loadedOrder && canCreate && (
          <div className="text-sm text-purple-700 bg-gradient-to-r from-purple-50 to-indigo-50 px-4 py-3 rounded-lg border border-purple-200 shadow-sm animate-pulse">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center mr-3">
                💡
              </div>
              <div>
                <div className="font-medium">ابدأ بإنشاء أمر تقييم جديد</div>
                <div className="text-xs text-purple-600 mt-1">اضغط على "إنشاء أمر جديد" لتفعيل وضع الإنشاء</div>
              </div>
            </div>
          </div>
        )}
      </div>

      <Card className="shadow-lg border-l-4 border-l-blue-500 hover:shadow-xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 py-2">
          <CardTitle className="text-sm text-blue-800 flex items-center">
            <div className="w-5 h-5 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">1</div>
            معلومات أمر التقييم الأساسية
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 pt-0">
          <div className="space-y-1">
            <Label htmlFor="eval_order_id_input" className="text-xs">رقم الأمر</Label>
            <Input
              id="eval_order_id_input"
              value={evaluationId}
              disabled
              className="h-8 text-xs"
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="eval_employee_input" className="text-xs">اسم المستخدم</Label>
            <Input
              id="eval_employee_input"
              value="مدير النظام"
              disabled
              className="h-8 text-xs"
            />
          </div>
          <div className="space-y-1">
            <Label htmlFor="eval_date_field" className="text-xs">التاريخ</Label>
            <Input
              id="eval_date_field"
              type="datetime-local"
              defaultValue={new Date().toISOString().slice(0, 16)}
              className="h-8 text-xs font-mono hover:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200"
              style={{ direction: 'ltr' }}
              disabled
            />
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg border-l-4 border-l-orange-500 hover:shadow-xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 py-2">
          <CardTitle className="text-sm text-orange-800 flex items-center">
            <div className="w-5 h-5 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">2</div>
            إدخال الرقم التسلسلي (IMEI) وتفاصيل الجهاز
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Label htmlFor="imei-input" className="whitespace-nowrap text-xs">
              إدخال IMEI:
            </Label>
            <Input
              id="imei-input"
              placeholder="أدخل 15 رقمًا..."
              value={imei}
              onChange={(e) => setImei(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleImeiSearch()}
              disabled={!!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
              className={`font-mono transition-all duration-300 text-gray-900 h-8 text-xs ${
                imei.length === 15
                  ? 'border-green-500 bg-green-50 shadow-md ring-2 ring-green-200 text-green-900'
                  : imei.length > 0
                  ? 'border-yellow-500 bg-yellow-50 shadow-sm ring-1 ring-yellow-200 text-yellow-900'
                  : 'hover:border-blue-300 focus:ring-2 focus:ring-blue-200 bg-white'
              }`}
            />
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileImport}
              accept=".txt"
            />
            <Button
              size="sm"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
              title="استيراد من ملف"
              disabled={!canCreate || (!isCreating && !loadedOrder)}
              className="border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200 h-8 px-2"
            >
              <Upload className="h-3 w-3" />
            </Button>
            <Button
              onClick={handleImeiSearch}
              disabled={!!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
              className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 h-8 px-3 text-xs"
            >
              بحث
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                setImei('');
                setCurrentDevice(null);
                setPreviousEvaluations([]);
              }}
              disabled={!currentDevice || (!canCreate || (!isCreating && !loadedOrder))}
              className="border-orange-300 text-orange-600 hover:bg-orange-50 hover:border-orange-400 transition-all duration-200 h-8 px-3 text-xs"
            >
              تغيير
            </Button>

            {/* عداد الأحرف التفاعلي */}
            <div className={`text-xs font-medium transition-colors duration-200 ${
              imei.length === 15
                ? 'text-green-600'
                : imei.length > 0
                ? 'text-yellow-600'
                : 'text-gray-500'
            }`}>
              <span className="inline-flex items-center">
                {imei.length === 15 && <span className="mr-1">✓</span>}
                {imei.length}/15 رقم
              </span>
            </div>
          </div>
          {currentDevice && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 rounded-md border p-4">
              <div>
                <span className="font-semibold">الرقم التسلسلي:</span>{' '}
                <Badge dir="ltr">{currentDevice.id}</Badge>
              </div>
              <div>
                <span className="font-semibold">الموديل:</span>{' '}
                {currentDevice.model}
              </div>
              {previousEvaluations.length > 0 && (
                <div className="md:col-span-2 mt-2">
                  <h4 className="font-semibold text-sm mb-1">
                    سجل التقييمات السابقة
                  </h4>
                  <ul className="list-disc pr-5 text-xs text-muted-foreground space-y-1">
                    {previousEvaluations.map((order) => (
                      <li key={order.id}>
                        <span className="font-medium">{order.orderId}</span>{' '}
                        بتاريخ{' '}
                        {new Date(order.date).toLocaleDateString('ar-EG')} -
                        النتيجة:{' '}
                        <Badge variant="secondary">
                          {
                            order.items.find((i) => i.deviceId === imei)
                              ?.finalGrade
                          }
                        </Badge>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {currentDevice && (
        <Card className="shadow-lg border-l-4 border-l-green-500 hover:shadow-xl transition-all duration-300">
          <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 py-2">
            <CardTitle className="text-sm text-green-800 flex items-center">
              <div className="w-5 h-5 bg-green-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">3</div>
              نظام التقييم الفني (Grading System)
            </CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-1">
              <Label className="text-xs">التقييم الخارجي</Label>
              <Select
                dir="rtl"
                value={formState.externalGrade}
                onValueChange={(v: EvaluationGrade) =>
                  setFormState((s) => ({ ...s, externalGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger className="h-8 text-xs hover:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200">
                  <SelectValue placeholder="اختر..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="بدون">بدون</SelectItem>
                  <SelectItem value="A++">A++</SelectItem>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                  <SelectItem value="D">D</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>تقييم الشاشة</Label>
              <Select
                dir="rtl"
                value={formState.screenGrade}
                onValueChange={(v: ScreenGrade) =>
                  setFormState((s) => ({ ...s, screenGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="بدون">بدون</SelectItem>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="M1">M1</SelectItem>
                  <SelectItem value="M2">M2</SelectItem>
                  <SelectItem value="M3">M3</SelectItem>
                  <SelectItem value="M4">M4</SelectItem>
                  <SelectItem value="N1">N1</SelectItem>
                  <SelectItem value="N2">N2</SelectItem>
                  <SelectItem value="F1">F1</SelectItem>
                  <SelectItem value="F2">F2</SelectItem>
                  <SelectItem value="F3">F3</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>الشبكة</Label>
              <Select
                dir="rtl"
                value={formState.networkGrade}
                onValueChange={(v: NetworkGrade) =>
                  setFormState((s) => ({ ...s, networkGrade: v }))
                }
                disabled={!canCreate || (!isCreating && !loadedOrder)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="مفتوح رسمي">مفتوح رسمي</SelectItem>
                  <SelectItem value="مغلق">مغلق</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>التقييم النهائي</Label>
              <Select
                dir="rtl"
                value={formState.finalGrade}
                onValueChange={(v: FinalGrade) =>
                  setFormState((s) => ({
                    ...s,
                    finalGrade: v,
                    fault: '',
                    customFault: '',
                    damage: '',
                    customDamage: '',
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="جاهز للبيع">جاهز للبيع</SelectItem>
                  <SelectItem value="يحتاج صيانة">يحتاج صيانة</SelectItem>
                  <SelectItem value="عيب فني">عيب فني</SelectItem>
                  <SelectItem value="تالف">تالف</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {(formState.finalGrade === 'يحتاج صيانة' ||
              formState.finalGrade === 'عيب فني') && (
              <>
                <div className="space-y-2 md:col-span-2">
                  <Label>تحديد نوع العطل</Label>
                  <Select
                    dir="rtl"
                    value={formState.fault}
                    onValueChange={(v: FaultType) =>
                      setFormState((s) => ({ ...s, fault: v }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع العطل..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="شاشة">شاشة</SelectItem>
                      <SelectItem value="بطارية">بطارية</SelectItem>
                      <SelectItem value="منفذ شحن">منفذ شحن</SelectItem>
                      <SelectItem value="كاميرا">كاميرا</SelectItem>
                      <SelectItem value="صوت">صوت</SelectItem>
                      <SelectItem value="لمس">لمس</SelectItem>
                      <SelectItem value="حساس">حساس</SelectItem>
                      <SelectItem value="هزاز">هزاز</SelectItem>
                      <SelectItem value="وايفاي">وايفاي</SelectItem>
                      <SelectItem value="ذاكره">ذاكره</SelectItem>
                      <SelectItem value="بطاقة sim">بطاقة SIM</SelectItem>
                      <SelectItem value="أعطال أخرى">أعطال أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formState.fault === 'أعطال أخرى' && (
                  <div className="space-y-2 md:col-span-2">
                    <Label>وصف العطل</Label>
                    <Input
                      placeholder="صف العطل..."
                      value={formState.customFault}
                      onChange={(e) =>
                        setFormState((s) => ({
                          ...s,
                          customFault: e.target.value,
                        }))
                      }
                    />
                  </div>
                )}
              </>
            )}

            {formState.finalGrade === 'تالف' && (
              <>
                <div className="space-y-2 md:col-span-2">
                  <Label>تحديد نوع التلف</Label>
                  <Select
                    dir="rtl"
                    value={formState.damage}
                    onValueChange={(v: DamageType) =>
                      setFormState((s) => ({ ...s, damage: v }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="اختر نوع التلف..." />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="شاشة">شاشة</SelectItem>
                      <SelectItem value="ماذر بورد">ماذر بورد</SelectItem>
                      <SelectItem value="الغرق">الغرق</SelectItem>
                      <SelectItem value="أخرى">أخرى</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {formState.damage === 'أخرى' && (
                  <div className="space-y-2 md:col-span-2">
                    <Label>وصف التلف</Label>
                    <Input
                      placeholder="صف التلف..."
                      value={formState.customDamage}
                      onChange={(e) =>
                        setFormState((s) => ({
                          ...s,
                          customDamage: e.target.value,
                        }))
                      }
                    />
                  </div>
                )}
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button onClick={handleAddToList}>
              <PlusCircle className="ml-2 h-4 w-4" /> إضافة للقائمة
            </Button>
          </CardFooter>
        </Card>
      )}

      <Card className="shadow-lg border-l-4 border-l-purple-500 hover:shadow-xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-purple-50 to-violet-50 py-2">
          <CardTitle className="text-sm text-purple-800 flex items-center">
            <div className="w-5 h-5 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">4</div>
            الأجهزة المضافة في الأمر الحالي
            <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium mr-2">
              ({evaluatedItems.length})
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border max-h-96 overflow-y-auto">
            <Table className="border-collapse border border-gray-300">
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-purple-50 to-violet-50 border-b-2 border-purple-200">
                  <TableHead className="border border-gray-300 text-center font-bold text-gray-700 bg-purple-200/70 py-2 text-sm w-12">
                    #
                  </TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">الرقم التسلسلي</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">الموديل</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">التقييم الخارجي</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">التقييم النهائي</TableHead>
                  <TableHead className="border border-gray-300 text-right font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">العطل/الوصف</TableHead>
                  <TableHead className="border border-gray-300 text-center font-semibold text-gray-700 bg-purple-100/50 py-2 text-sm">إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {evaluatedItems.length === 0 ? (
                  <TableRow className="hover:bg-gray-50">
                    <TableCell colSpan={7} className="h-24 text-center border border-gray-300 text-gray-500 italic">
                      لم يتم إضافة أي أجهزة بعد.
                    </TableCell>
                  </TableRow>
                ) : (
                  evaluatedItems.map((item, index) => (
                    <TableRow
                      key={item.deviceId}
                      className={`
                        hover:bg-purple-50 transition-colors duration-200
                        ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}
                      `}
                    >
                      <TableCell className="border border-gray-300 text-center text-gray-700 font-bold py-2 text-sm w-12 bg-gray-100">
                        {index + 1}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right font-mono text-blue-700 py-2 text-sm bg-white" dir="ltr">
                        {item.deviceId}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 text-sm text-gray-800 bg-white">
                        {item.model}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 text-sm text-gray-800 bg-white">
                        {item.externalGrade}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 bg-white">
                        <Badge
                          variant={
                            item.finalGrade === 'جاهز للبيع'
                              ? 'default'
                              : item.finalGrade === 'يحتاج صيانة'
                                ? 'secondary'
                                : 'destructive'
                          }
                          className="px-2 py-1 text-xs"
                        >
                          {item.finalGrade}
                        </Badge>
                      </TableCell>
                      <TableCell className="border border-gray-300 text-right py-2 text-sm text-gray-800 bg-white">
                        {item.fault || item.damageType || 'N/A'}
                      </TableCell>
                      <TableCell className="border border-gray-300 text-center py-2 bg-white">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-red-500 hover:text-red-700 hover:bg-red-50 transition-all duration-200 rounded-full h-6 w-6"
                          onClick={() => handleRemoveItem(item.deviceId)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      <Card className="shadow-lg border-l-4 border-l-teal-500 hover:shadow-xl transition-all duration-300">
        <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 py-2">
          <CardTitle className="text-sm text-teal-800 flex items-center">
            <div className="w-5 h-5 bg-teal-500 text-white rounded-full flex items-center justify-center text-xs font-bold mr-2">5</div>
            ملاحظات عامة
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="أضف أي ملاحظات إضافية حول عملية التقييم..."
            value={generalNotes}
            onChange={(e) => setGeneralNotes(e.target.value)}
          />
        </CardContent>
      </Card>

      <div className="flex flex-wrap justify-start gap-2">
        <Button
          variant="destructive"
          onClick={() => setIsCancelAlertOpen(true)}
          className="bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        >
          <X className="ml-2 h-4 w-4" /> إلغاء الأمر
        </Button>
        <Button
          variant="outline"
          onClick={() => handleExport('print')}
          disabled={evaluatedItems.length === 0}
          className="border-blue-300 text-blue-600 hover:bg-blue-50 hover:border-blue-400 transition-all duration-200"
        >
          <Printer className="ml-2 h-4 w-4" /> طباعة التقرير
        </Button>
        <Button
          variant="outline"
          onClick={() => handleExport('download')}
          disabled={evaluatedItems.length === 0}
          className="border-red-300 text-red-600 hover:bg-red-50 hover:border-red-400 transition-all duration-200"
        >
          <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
        </Button>
        <Button
          variant="outline"
          onClick={handleExportExcel}
          disabled={evaluatedItems.length === 0}
          className="border-green-300 text-green-600 hover:bg-green-50 hover:border-green-400 transition-all duration-200"
        >
          <FileSpreadsheet className="ml-2 h-4 w-4" /> تصدير Excel
        </Button>
        {((canCreate && isCreating) || (canEdit && loadedOrder)) && (
          <Button
            onClick={handleSaveEvaluation}
            disabled={evaluatedItems.length === 0}
            className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
          >
            <Save className="ml-2 h-4 w-4" />{' '}
            {loadedOrder ? 'تحديث الأمر' : 'حفظ التقييم'}
          </Button>
        )}
      </div>

      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد؟</AlertDialogTitle>
          </AlertDialogHeader>
          <AlertDialogDescription>
            سيتم فقدان جميع البيانات غير المحفوظة في أمر التقييم الحالي.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={resetPage}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <Dialog
        open={isLoadOrderDialogOpen}
        onOpenChange={setIsLoadOrderDialogOpen}
      >
        <DialogContent className="sm:max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="text-lg font-bold text-green-800">تحميل أمر تقييم سابق</DialogTitle>
            <DialogDescription className="text-green-600">
              اختر أمر تقييم لتحميل بياناته أو تعديله.
            </DialogDescription>
          </DialogHeader>

          {/* Container with fixed height and scroll */}
          <div className="h-[400px] overflow-y-auto border rounded-lg">
            <Table className="border-collapse">
              <TableHeader className="sticky top-0 z-10">
                <TableRow className="bg-gradient-to-r from-green-50 to-emerald-50 border-b-2 border-green-200">
                  <TableHead className="border-b border-gray-300 text-center font-bold text-gray-700 bg-green-200/70 py-2 text-sm w-12">
                    #
                  </TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-green-100/90 py-2 text-sm">رقم الأمر</TableHead>
                  <TableHead className="border-b border-gray-300 text-right font-semibold text-gray-700 bg-green-100/90 py-2 text-sm">التاريخ</TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-green-100/90 py-2 text-sm">عدد الأجهزة</TableHead>
                  <TableHead className="border-b border-gray-300 text-center font-semibold text-gray-700 bg-green-100/90 py-2 text-sm">إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {evaluationOrders.length === 0 ? (
                  <TableRow className="hover:bg-gray-50">
                    <TableCell colSpan={5} className="h-24 text-center border-b border-gray-200 text-gray-500 italic">
                      لا توجد أوامر تقييم سابقة.
                    </TableCell>
                  </TableRow>
                ) : (
                  evaluationOrders.map((order, index) => (
                    <TableRow
                      key={order.id}
                      className="hover:bg-green-50 transition-colors duration-200 cursor-pointer h-12"
                    >
                      <TableCell className="border-b border-gray-200 text-center text-gray-700 font-bold py-2 text-sm bg-gray-100">
                        {index + 1}
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-right font-mono text-blue-600 font-medium py-2 text-sm">
                        {order.orderId}
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-right font-mono text-gray-600 py-2 text-sm" style={{ direction: 'ltr' }}>
                        {formatDateTime(order.date)}
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-center py-2 text-sm">
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          {order.items.length} جهاز
                        </span>
                      </TableCell>
                      <TableCell className="border-b border-gray-200 text-center py-2">
                        <Button
                          size="sm"
                          onClick={() => handleLoadOrder(order)}
                          className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-sm hover:shadow-md transition-all duration-200 h-7 px-3 text-xs"
                        >
                          تحميل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog
        open={!!orderToDelete}
        onOpenChange={() => setOrderToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              هذا الإجراء لا يمكن التراجع عنه. سيتم حذف أمر التقييم بشكل دائم.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteOrder}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
