import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

// مجلد المرفقات
const ATTACHMENTS_DIR = path.join(process.cwd(), 'public', 'attachments');

// التأكد من وجود مجلد المرفقات
async function ensureAttachmentsDir() {
  if (!existsSync(ATTACHMENTS_DIR)) {
    await mkdir(ATTACHMENTS_DIR, { recursive: true });
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureAttachmentsDir();

    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'لم يتم العثور على ملف' }, { status: 400 });
    }

    // إنشاء اسم ملف فريد
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = path.extname(file.name);
    const fileName = `${timestamp}_${randomString}${fileExtension}`;
    
    // مسار الملف
    const filePath = path.join(ATTACHMENTS_DIR, fileName);
    
    // تحويل الملف إلى Buffer وحفظه
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    await writeFile(filePath, buffer);
    
    // إرجاع معلومات الملف
    return NextResponse.json({
      success: true,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type,
      url: `/attachments/${fileName}`
    });

  } catch (error) {
    console.error('خطأ في رفع الملف:', error);
    return NextResponse.json({ error: 'فشل في رفع الملف' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('file');
    
    if (!fileName) {
      return NextResponse.json({ error: 'اسم الملف مطلوب' }, { status: 400 });
    }

    const filePath = path.join(ATTACHMENTS_DIR, fileName);
    
    if (!existsSync(filePath)) {
      return NextResponse.json({ error: 'الملف غير موجود' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      url: `/attachments/${fileName}`
    });

  } catch (error) {
    console.error('خطأ في جلب الملف:', error);
    return NextResponse.json({ error: 'فشل في جلب الملف' }, { status: 500 });
  }
}
