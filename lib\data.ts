import {
  Device,
  Contact,
  Warehouse,
  Sale,
  Return,
  Manufacturer,
  DeviceModel,
  SupplyOrder,
  EvaluationOrder,
  WarehouseTransfer,
  User,
  AppPermissions,
  SystemSettings,
  EmployeeRequest,
  InternalMessage,
  AcceptanceOrder,
  MaintenanceOrder,
  DeliveryOrder,
} from './types';

export const initialDevices: Device[] = [
  {
    id: '111111111111111',
    model: 'iPhone 14 Pro',
    status: 'متاح للبيع',
    storage: '256GB',
    price: 950,
    warehouseId: 1,
    supplierId: 1,
    condition: 'جديد',
    dateAdded: '2023-10-01T10:00:00.000Z',
  },
  {
    id: '222222222222222',
    model: 'Samsung Galaxy S23',
    status: 'متاح للبيع',
    storage: '128GB',
    price: 800,
    warehouseId: 1,
    supplierId: 1,
    condition: 'جديد',
    dateAdded: '2023-10-01T10:00:00.000Z',
  },
  {
    id: '333333333333333',
    model: 'iPhone 13',
    status: 'تحتاج صيانة',
    storage: '128GB',
    price: 600,
    warehouseId: 2,
    supplierId: 2,
    condition: 'مستخدم',
    dateAdded: '2023-10-02T11:00:00.000Z',
  },
  {
    id: '444444444444444',
    model: 'Google Pixel 7',
    status: 'مباع',
    storage: '256GB',
    price: 750,
    warehouseId: 2,
    supplierId: 2,
    condition: 'مستخدم',
    dateAdded: '2023-10-02T11:00:00.000Z',
  },
  {
    id: '555555555555555',
    model: 'iPhone 14 Pro Max',
    status: 'متاح للبيع',
    storage: '512GB',
    price: 1100,
    warehouseId: 1,
    supplierId: 1,
    condition: 'جديد',
    dateAdded: '2023-10-03T12:00:00.000Z',
  },
  {
    id: '666666666666666',
    model: 'Samsung Galaxy Z Fold 4',
    status: 'تحتاج صيانة',
    storage: '256GB',
    price: 1300,
    warehouseId: 3,
    supplierId: 2,
    condition: 'مستخدم',
    dateAdded: '2023-10-03T12:00:00.000Z',
  },
];

export const initialClients: Contact[] = [
  {
    id: 1,
    name: 'شركة النور للتكنولوجيا',
    phone: '777123456',
    email: '<EMAIL>',
  },
  {
    id: 2,
    name: 'مؤسسة الأفق الرقمي',
    phone: '777234567',
    email: '<EMAIL>',
  },
  {
    id: 3,
    name: 'خالد محمد',
    phone: '777345678',
    email: '<EMAIL>',
  },
];

export const initialSuppliers: Contact[] = [
  {
    id: 1,
    name: 'Tech Imports',
    phone: '+97141234567',
    email: '<EMAIL>',
  },
  {
    id: 2,
    name: 'Global Gadgets LLC',
    phone: '+97142345678',
    email: '<EMAIL>',
  },
];

export const initialWarehouses: Warehouse[] = [
  {
    id: 1,
    name: 'المخزن الرئيسي',
    type: 'رئيسي',
    location: 'صنعاء - شارع الخمسين',
  },
  { id: 2, name: 'فرع وسط المدينة', type: 'فرعي', location: 'صنعاء - التحرير' },
  { id: 3, name: 'فرع حدة', type: 'فرعي', location: 'صنعاء - شارع حدة' },
];

export const initialSales: Sale[] = [
  {
    id: 1,
    soNumber: 'SO-001',
    clientName: 'عميل اختباري',
    clientPhone: '777123456',
    date: '2024-01-20',
    items: [
      {
        deviceId: '111111111111111',
        model: 'iPhone 14 Pro',
        price: 950,
        storage: '256GB',
        condition: 'جديد'
      }
    ],
    totalAmount: 950,
    notes: 'مبيعات اختبارية'
  }
];
export const initialReturns: Return[] = [];
export const initialAcceptanceOrders: AcceptanceOrder[] = [];

export const initialManufacturers: Manufacturer[] = [
  { id: 1, name: 'Apple' },
  { id: 2, name: 'Samsung' },
  { id: 3, name: 'Google' },
];

export const initialDeviceModels: DeviceModel[] = [
  { id: 1, manufacturerId: 1, name: 'iPhone 14 Pro' },
  { id: 2, manufacturerId: 1, name: 'iPhone 13' },
  { id: 3, manufacturerId: 1, name: 'iPhone 14 Pro Max' },
  { id: 4, manufacturerId: 2, name: 'Galaxy S23' },
  { id: 5, manufacturerId: 2, name: 'Galaxy Z Fold 4' },
  { id: 6, manufacturerId: 3, name: 'Pixel 7' },
];

export const initialSupplyOrders: SupplyOrder[] = [
  {
    id: 1,
    supplyOrderId: 'SUP-1',
    supplierId: 1,
    invoiceNumber: 'INV-001',
    supplyDate: '2024-01-15T10:30',
    warehouseId: 1,
    employeeName: 'مدير النظام',
    items: [
      {
        imei: '111111111111111',
        manufacturer: 'Apple',
        model: 'iPhone 14 Pro',
        condition: 'جديد'
      },
      {
        imei: '222222222222222',
        manufacturer: 'Samsung',
        model: 'Galaxy S23',
        condition: 'جديد'
      }
    ],
    notes: 'أمر توريد اختباري',
    createdAt: '2024-01-15T10:00:00.000Z'
  }
];
export const initialEvaluationOrders: EvaluationOrder[] = [];
export const initialMaintenanceOrders: MaintenanceOrder[] = [];
export const initialDeliveryOrders: DeliveryOrder[] = [];
export const initialWarehouseTransfers: WarehouseTransfer[] = [];
export const initialEmployeeRequests: EmployeeRequest[] = [];
export const initialInternalMessages: InternalMessage[] = [];

export const initialSystemSettings: SystemSettings = {
  logoUrl: '',
  companyNameAr: 'DeviceFlow',
  companyNameEn: 'DeviceFlow',
  addressAr: 'الشارع الرئيسي، المدينة، الدولة',
  addressEn: 'Main Street, City, Country',
  phone: '+************',
  email: '<EMAIL>',
  website: 'www.deviceflow.com',
  footerTextAr: 'شكرًا لتعاملكم معنا.',
  footerTextEn: 'Thank you for your business.',
};

const fullPermissions: AppPermissions = {
  dashboard: { view: true, create: true, edit: true, delete: true },
  track: { view: true, create: true, edit: true, delete: true },
  supply: { view: true, create: true, edit: true, delete: true },
  grading: { view: true, create: true, edit: true, delete: true },
  inventory: { view: true, create: true, edit: true, delete: true },
  sales: { view: true, create: true, edit: true, delete: true },
  maintenance: { view: true, create: true, edit: true, delete: true },
  maintenanceTransfer: { view: true, create: true, edit: true, delete: true },
  warehouseTransfer: { view: true, create: true, edit: true, delete: true },
  clients: { view: true, create: true, edit: true, delete: true },
  pricing: { view: true, create: true, edit: true, delete: true },
  returns: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    acceptWithoutWarranty: true,
  },
  warehouses: { view: true, create: true, edit: true, delete: true },
  users: { view: true, create: true, edit: true, delete: true },
  reports: { view: true, create: true, edit: true, delete: true },
  stocktaking: { view: true, create: true, edit: true, delete: true },
  acceptDevices: { view: true, create: true, edit: true, delete: true },
  settings: { view: true, create: true, edit: true, delete: true },
  requests: { view: true, create: true, edit: true, delete: true },
  messaging: {
    view: true,
    create: true,
    edit: true,
    delete: true,
    viewAll: true,
  },
};

const salesPermissions: AppPermissions = {
  dashboard: { view: true, create: false, edit: false, delete: false },
  track: { view: true, create: false, edit: false, delete: false },
  supply: { view: false, create: false, edit: false, delete: false },
  grading: { view: false, create: false, edit: false, delete: false },
  inventory: { view: true, create: false, edit: false, delete: false },
  sales: { view: true, create: true, edit: true, delete: true },
  maintenance: { view: false, create: false, edit: false, delete: false },
  maintenanceTransfer: {
    view: false,
    create: false,
    edit: false,
    delete: false,
  },
  warehouseTransfer: { view: false, create: false, edit: false, delete: false },
  clients: { view: true, create: true, edit: true, delete: false },
  pricing: { view: true, create: false, edit: false, delete: false },
  returns: {
    view: true,
    create: true,
    edit: true,
    delete: false,
    acceptWithoutWarranty: false,
  },
  warehouses: { view: false, create: false, edit: false, delete: false },
  users: { view: false, create: false, edit: false, delete: false },
  reports: { view: true, create: false, edit: false, delete: false },
  stocktaking: { view: false, create: false, edit: false, delete: false },
  acceptDevices: { view: false, create: false, edit: false, delete: false },
  settings: { view: false, create: false, edit: false, delete: false },
  requests: { view: true, create: true, edit: false, delete: false },
  messaging: {
    view: true,
    create: true,
    edit: false,
    delete: false,
    viewAll: false,
  },
};

export const noPermissions: AppPermissions = Object.keys(
  fullPermissions,
).reduce((acc, key) => {
  acc[key as keyof AppPermissions] = {
    view: false,
    create: false,
    edit: false,
    delete: false,
  };
  return acc;
}, {} as AppPermissions);

export const initialUsers: User[] = [
  {
    id: 1,
    name: 'مدير النظام',
    username: 'admin',
    email: '<EMAIL>',
    permissions: fullPermissions,
  },
  {
    id: 2,
    name: 'موظف مبيعات',
    username: 'sales',
    email: '<EMAIL>',
    permissions: salesPermissions,
  },
  {
    id: 3,
    name: 'فني صيانة',
    username: 'maint',
    email: '<EMAIL>',
    permissions: noPermissions,
  },
  {
    id: 4,
    name: 'أمين مخزن',
    username: 'wh',
    email: '<EMAIL>',
    permissions: noPermissions,
  },
];
