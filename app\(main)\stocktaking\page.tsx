'use client';

import { useState, useMemo, useRef, useEffect } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type {
  Device,
  SystemSettings,
  StocktakeDraft,
  StocktakeHistory,
  Warehouse,
} from '@/lib/types';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  ClipboardList,
  Upload,
  FileDown,
  Printer,
  FileSpreadsheet,
  Check,
  X,
  AlertTriangle,
  PackageSearch,
  Save,
  FolderOpen,
  Trash,
  Trash2,
  ClipboardPaste,
  PlusCircle,
  Eye,
  Package,
  ShoppingCart,
  Wrench,
  Search,
  ChevronsUpDown,
  Edit,
} from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import * as XLSX from 'xlsx';
import {
  Command,
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

type StocktakeResultList = {
  deviceId: string;
  model: string;
  status: string;
  warehouseName?: string;
  note?: string;
};

type StocktakeResults = {
  expectedCount: number;
  matching: StocktakeResultList[];
  missing: StocktakeResultList[];
  extra: StocktakeResultList[];
  soldButFound: StocktakeResultList[];
  inMaintenance: StocktakeResultList[];
};

const initialResultsState: StocktakeResults = {
  expectedCount: 0,
  matching: [],
  missing: [],
  extra: [],
  soldButFound: [],
  inMaintenance: [],
};

type ResultDetails = {
  title: string;
  items: StocktakeResultList[];
};

// Component for the second-level details dialog (List of Devices)
const DeviceListDialog = ({
  isOpen,
  onClose,
  details,
}: {
  isOpen: boolean;
  onClose: () => void;
  details: ResultDetails | null;
}) => {
  const { systemSettings } = useStore();

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!details) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text(details.title, 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [['الملاحظة', 'الحالة', 'الموديل', 'الرقم التسلسلي']],
        body: details.items.map((item) => [
          item.note || '-',
          item.status,
          item.model,
          item.deviceId,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
      });
      doc.save(`${details.title.replace(/\s+/g, '_')}.pdf`);
    } else {
      const dataToExport = details.items.map((item) => ({
        'الرقم التسلسلي': item.deviceId,
        الموديل: item.model,
        الحالة: item.status,
        المخزن: item.warehouseName,
        الملاحظة: item.note,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Devices');
      XLSX.writeFile(
        workbook,
        `${details.title.replace(/\s+/g, '_')}.xlsx`,
      );
    }
  };

  const handlePrint = () => {
    if (!details) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text(details.title, 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [['الملاحظة', 'الحالة', 'الموديل', 'الرقم التسلسلي']],
      body: details.items.map((item) => [
        item.note || '-',
        item.status,
        item.model,
        item.deviceId,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!details) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>{details.title}</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] pr-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الرقم التسلسلي</TableHead>
                <TableHead>الموديل</TableHead>
                <TableHead>الحالة</TableHead>
                <TableHead>المخزن</TableHead>
                <TableHead>ملاحظة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {details.items.map((item) => (
                <TableRow key={item.deviceId}>
                  <TableCell dir="ltr">{item.deviceId}</TableCell>
                  <TableCell>{item.model}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.status}</Badge>
                  </TableCell>
                  <TableCell>{item.warehouseName || '-'}</TableCell>
                  <TableCell>{item.note || '-'}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
        <DialogFooter className="justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('pdf')}>
              <FileDown className="ml-2 h-4 w-4" /> PDF
            </Button>
            <Button variant="outline" onClick={() => handleExport('excel')}>
              <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
            </Button>
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
          </div>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Component for the first-level details dialog (Grouped by Model)
const ResultSummaryDialog = ({
  isOpen,
  onClose,
  details,
}: {
  isOpen: boolean;
  onClose: () => void;
  details: ResultDetails | null;
}) => {
  const [deviceListDetails, setDeviceListDetails] = useState<{
    title: string;
    items: StocktakeResultList[];
  } | null>(null);

  const { systemSettings } = useStore();

  const groupedData = useMemo(() => {
    if (!details) return [];
    return Object.values(
      details.items.reduce(
        (acc, item) => {
          const key = `${item.model}-${item.warehouseName || 'N/A'}`;
          if (!acc[key]) {
            acc[key] = {
              model: item.model,
              warehouseName: item.warehouseName,
              count: 0,
              items: [],
            };
          }
          acc[key].count++;
          acc[key].items.push(item);
          return acc;
        },
        {} as Record<
          string,
          {
            model: string;
            warehouseName?: string;
            count: number;
            items: StocktakeResultList[];
          }
        >,
      ),
    );
  }, [details]);

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!details) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text(details.title, 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [['الموديل', 'المخزن', 'العدد']],
        body: groupedData.map((item) => [
          item.model,
          item.warehouseName || 'غير محدد',
          item.count,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      });
      doc.save(`${details.title.replace(/\s+/g, '_')}_summary.pdf`);
    } else {
      const dataToExport = groupedData.map((item) => ({
        الموديل: item.model,
        المخزن: item.warehouseName || 'غير محدد',
        العدد: item.count,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Summary');
      XLSX.writeFile(
        workbook,
        `${details.title.replace(/\s+/g, '_')}_summary.xlsx`,
      );
    }
  };

  const handlePrint = () => {
    if (!details) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text(details.title, 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [['الموديل', 'المخزن', 'العدد']],
      body: groupedData.map((item) => [
        item.model,
        item.warehouseName || 'غير محدد',
        item.count,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!details) return null;

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-3xl">
          <DialogHeader>
            <DialogTitle>{details.title}</DialogTitle>
            <DialogDescription>
              ملخص الأجهزة مجمعة حسب الموديل والمخزن.
            </DialogDescription>
          </DialogHeader>
          <ScrollArea className="max-h-[60vh] pr-4">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>الموديل</TableHead>
                  <TableHead>المخزن</TableHead>
                  <TableHead>العدد</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {groupedData.length > 0 ? (
                  groupedData.map((group) => (
                    <TableRow
                      key={`${group.model}-${group.warehouseName || 'N/A'}`}
                    >
                      <TableCell>{group.model}</TableCell>
                      <TableCell>{group.warehouseName || 'غير محدد'}</TableCell>
                      <TableCell>
                        <Badge>{group.count}</Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            setDeviceListDetails({
                              title: `تفاصيل ${group.model} - ${details.title}`,
                              items: group.items,
                            })
                          }
                        >
                          <Eye className="h-4 w-4 mr-2" /> عرض التفاصيل
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      لا توجد بيانات لعرضها.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </ScrollArea>
          <DialogFooter className="justify-between">
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleExport('pdf')}>
                <FileDown className="ml-2 h-4 w-4" /> PDF
              </Button>
              <Button variant="outline" onClick={() => handleExport('excel')}>
                <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
              </Button>
              <Button variant="outline" onClick={handlePrint}>
                <Printer className="ml-2 h-4 w-4" /> طباعة
              </Button>
            </div>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {deviceListDetails && (
        <DeviceListDialog
          isOpen={!!deviceListDetails}
          onClose={() => setDeviceListDetails(null)}
          details={deviceListDetails}
        />
      )}
    </>
  );
};

// Component for the new summary modal
const CumulativeSummaryDialog = ({
  isOpen,
  onClose,
  results,
  processedSerials,
  devices,
  warehouses,
}: {
  isOpen: boolean;
  onClose: () => void;
  results: StocktakeResults | null;
  processedSerials: Set<string>;
  devices: Device[];
  warehouses: Warehouse[];
}) => {
  const summaryByModelAndWarehouse = useMemo(() => {
    if (!results) return [];

    const summaryMap: Record<
      string,
      {
        model: string;
        warehouseName: string;
        total: number;
        entered: number;
        missing: number;
      }
    > = {};

    const expectedDevicesInScope = devices.filter(
      (d) =>
        results.matching.some((m) => m.deviceId === d.id) ||
        results.missing.some((m) => m.deviceId === d.id),
    );

    // Initialize with total expected devices
    expectedDevicesInScope.forEach((device) => {
      const warehouseName =
        warehouses.find((w) => w.id === device.warehouseId)?.name || 'غير محدد';
      const key = `${device.model}-${warehouseName}`;

      if (!summaryMap[key]) {
        summaryMap[key] = {
          model: device.model,
          warehouseName,
          total: 0,
          entered: 0,
          missing: 0,
        };
      }
      summaryMap[key].total++;
    });

    // Count entered devices
    processedSerials.forEach((serial) => {
      const device = devices.find((d) => d.id === serial);
      if (device && expectedDevicesInScope.some((d) => d.id === serial)) {
        const warehouseName =
          warehouses.find((w) => w.id === device.warehouseId)?.name ||
          'غير محدد';
        const key = `${device.model}-${warehouseName}`;
        if (summaryMap[key]) {
          summaryMap[key].entered++;
        }
      }
    });

    // Calculate missing
    Object.values(summaryMap).forEach((summary) => {
      summary.missing = summary.total - summary.entered;
    });

    return Object.values(summaryMap);
  }, [results, processedSerials, devices, warehouses]);

  const handleExport = (format: 'pdf' | 'excel') => {
    if (!summaryByModelAndWarehouse.length) return;

    if (format === 'pdf') {
      const doc = new jsPDF();
      doc.setR2L(true);
      doc.setFont('Amiri-Regular');
      doc.text('ملخص الجرد التراكمي', 105, 15, { align: 'center' });
      autoTable(doc, {
        startY: 25,
        head: [
          [
            'الموديل',
            'المخزن',
            'إجمالي المخزون',
            'الأجهزة المدخلة',
            'الأجهزة المفقودة',
          ],
        ],
        body: summaryByModelAndWarehouse.map((item) => [
          item.model,
          item.warehouseName,
          item.total,
          item.entered,
          item.missing,
        ]),
        styles: { font: 'Amiri-Regular', halign: 'right' },
        headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      });
      doc.save('cumulative_stocktake_summary.pdf');
    } else {
      const dataToExport = summaryByModelAndWarehouse.map((item) => ({
        الموديل: item.model,
        المخزن: item.warehouseName,
        'إجمالي المخزون': item.total,
        'الأجهزة المدخلة': item.entered,
        'الأجهزة المفقودة': item.missing,
      }));
      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'ملخص الجرد');
      XLSX.writeFile(workbook, 'cumulative_stocktake_summary.xlsx');
    }
  };

  const handlePrint = () => {
    if (!summaryByModelAndWarehouse.length) return;
    const doc = new jsPDF();
    doc.setR2L(true);
    doc.setFont('Amiri-Regular');
    doc.text('ملخص الجرد التراكمي', 105, 15, { align: 'center' });
    autoTable(doc, {
      startY: 25,
      head: [
        [
          'الموديل',
          'المخزن',
          'إجمالي المخزون',
          'الأجهزة المدخلة',
          'الأجهزة المفقودة',
        ],
      ],
      body: summaryByModelAndWarehouse.map((item) => [
        item.model,
        item.warehouseName,
        item.total,
        item.entered,
        item.missing,
      ]),
      styles: { font: 'Amiri-Regular', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
    });
    doc.autoPrint();
    doc.output('dataurlnewwindow');
  };

  if (!results) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-3xl">
        <DialogHeader>
          <DialogTitle>ملخص الجرد التراكمي</DialogTitle>
          <DialogDescription>
            مقارنة بين الأجهزة التي تم إدخالها والأجهزة المفقودة لكل موديل
            ومخزن.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] pr-4">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>الموديل</TableHead>
                <TableHead>المخزن</TableHead>
                <TableHead>إجمالي المخزون</TableHead>
                <TableHead>الأجهزة المدخلة</TableHead>
                <TableHead>الأجهزة المفقودة</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {summaryByModelAndWarehouse.map((item, index) => (
                <TableRow key={index}>
                  <TableCell>{item.model}</TableCell>
                  <TableCell>{item.warehouseName}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{item.total}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="default">{item.entered}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant={item.missing > 0 ? 'destructive' : 'default'}
                      className={
                        item.missing === 0
                          ? 'bg-green-500/20 text-green-500'
                          : ''
                      }
                    >
                      {item.missing}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </ScrollArea>
        <DialogFooter className="justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => handleExport('pdf')}>
              <FileDown className="ml-2 h-4 w-4" /> PDF
            </Button>
            <Button variant="outline" onClick={() => handleExport('excel')}>
              <FileSpreadsheet className="ml-2 h-4 w-4" /> Excel
            </Button>
            <Button variant="outline" onClick={handlePrint}>
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
          </div>
          <DialogClose asChild>
            <Button variant="outline">إغلاق</Button>
          </DialogClose>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};


// Component for showing details of a historic stocktake
const HistoryDetailsDialog = ({
  isOpen,
  onClose,
  historyEntry,
  onReopen,
}: {
  isOpen: boolean;
  onClose: () => void;
  historyEntry: StocktakeHistory | null;
  onReopen: (entry: StocktakeHistory) => void;
}) => {
  const [resultDetails, setResultDetails] = useState<ResultDetails | null>(
    null,
  );
  if (!historyEntry) return null;

  const handleResultCardClick = (
    title: string,
    items: StocktakeResultList[] | undefined,
  ) => {
    if (!items || items.length === 0) {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
      return;
    }
    setResultDetails({ title, items });
  };

  const renderResultCard = (
    title: string,
    count: number,
    icon: React.ReactNode,
    className: string,
    items: StocktakeResultList[] | undefined,
  ) => (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md hover:-translate-y-1',
        className,
      )}
      onClick={() => handleResultCardClick(title, items)}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count}</div>
      </CardContent>
    </Card>
  );

  return (
    <>
      <ResultSummaryDialog
        isOpen={!!resultDetails}
        onClose={() => setResultDetails(null)}
        details={resultDetails}
      />
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>تفاصيل الجرد: {historyEntry.id}</DialogTitle>
            <DialogDescription>
              تاريخ الإكمال:{' '}
              {new Date(historyEntry.completedAt).toLocaleString('ar-EG', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
              })}{' '}
              | بواسطة: {historyEntry.userName}
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 my-4">
            {renderResultCard(
              'أجهزة مطابقة',
              historyEntry.summary.matched,
              <Check className="h-4 w-4 text-green-500" />,
              'bg-green-500/10 border-green-500/20',
              historyEntry.result?.matching,
            )}
            {renderResultCard(
              'أجهزة مفقودة',
              historyEntry.summary.missing,
              <X className="h-4 w-4 text-red-500" />,
              'bg-red-500/10 border-red-500/20',
              historyEntry.result?.missing,
            )}
            {renderResultCard(
              'أجهزة زائدة/خاطئة',
              historyEntry.summary.extra,
              <AlertTriangle className="h-4 w-4 text-yellow-500" />,
              'bg-yellow-500/10 border-yellow-500/20',
              historyEntry.result?.extra,
            )}
            {renderResultCard(
              'مباعة وموجودة',
              historyEntry.summary.sold,
              <ShoppingCart className="h-4 w-4 text-purple-500" />,
              'bg-purple-500/10 border-purple-500/20',
              historyEntry.result?.soldButFound,
            )}
            {renderResultCard(
              'موجودة في الصيانة',
              historyEntry.summary.inMaintenance,
              <Wrench className="h-4 w-4 text-blue-500" />,
              'bg-blue-500/10 border-blue-500/20',
              historyEntry.result?.inMaintenance,
            )}
          </div>
          <DialogFooter className="justify-between">
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <FileDown className="ml-2 h-4 w-4" /> تصدير التقرير
              </Button>
              <Button variant="outline" size="sm">
                <Printer className="ml-2 h-4 w-4" /> طباعة
              </Button>
            </div>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => onReopen(historyEntry)}
            >
              <Edit className="ml-2 h-4 w-4" /> فتح وتعديل الأمر
            </Button>
            <Button variant="outline" onClick={onClose}>
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default function StocktakingPage() {
  const { devices, warehouses, systemSettings, currentUser } = useStore();
  const { toast } = useToast();

  const [stocktakeId, setStocktakeId] = useState('');
  const [notes, setNotes] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [warehouseScope, setWarehouseScope] = useState<'all' | 'selected'>(
    'all',
  );
  const [selectedWarehouses, setSelectedWarehouses] = useState<number[]>([]);
  const [includeMaintenance, setIncludeMaintenance] = useState(false);
  const [modelScope, setModelScope] = useState<'all' | 'selected'>('all');
  const [selectedModels, setSelectedModels] = useState<number[]>([]);
  const [scannedItems, setScannedItems] = useState<string[]>([]);
  const [imeiInput, setImeiInput] = useState('');
  const [results, setResults] = useState<StocktakeResults | null>(null);

  const [showModelDialog, setShowModelDialog] = useState(false);
  const [isHistoryModalOpen, setIsHistoryModalOpen] = useState(false);
  const [isDraftsModalOpen, setIsDraftsModalOpen] = useState(false);
  const [isPasteModalOpen, setIsPasteModalOpen] = useState(false);
  const [pastedImeis, setPastedImeis] = useState('');
  const [selectedHistory, setSelectedHistory] =
    useState<StocktakeHistory | null>(null);
  const [showHistoryDetailsModal, setShowHistoryDetailsModal] = useState(false);
  const [drafts, setDrafts] = useState<StocktakeDraft[]>([]);
  const [history, setHistory] = useState<StocktakeHistory[]>([]);
  const [isCancelAlertOpen, setIsCancelAlertOpen] = useState(false);
  const [isDeleteAlertOpen, setIsDeleteAlertOpen] = useState(false);


  const [processedSerialsInSession, setProcessedSerialsInSession] = useState<
    Set<string>
  >(new Set());
  const [duplicateImeis, setDuplicateImeis] = useState<string[]>([]);
  const [invalidImeis, setInvalidImeis] = useState<string[]>([]);
  const [showDetailsDialog, setShowDetailsDialog] = useState(false);
  const [dialogTitle, setDialogTitle] = useState('');
  const [dialogItems, setDialogItems] = useState<string[]>([]);
  const [resultDetails, setResultDetails] = useState<ResultDetails | null>(
    null,
  );
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);


  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateNewStocktakeId = (
    currentHistory: StocktakeHistory[],
    currentDrafts: StocktakeDraft[],
  ) => {
    const historyIds = currentHistory
      .map((h) => parseInt(h.id.replace('STK-', ''), 10))
      .filter((n) => !isNaN(n));
    const draftIds = currentDrafts
      .map((d) => parseInt(d.id.replace('STK-', ''), 10))
      .filter((n) => !isNaN(n));
    const allIds = [...historyIds, ...draftIds];
    const maxId = allIds.length > 0 ? Math.max(...allIds) : 0;
    return `STK-${maxId + 1}`;
  };

  useEffect(() => {
    // Load drafts and history from local storage on mount
    const savedDrafts = localStorage.getItem('stocktakeDrafts');
    const loadedDrafts = savedDrafts ? JSON.parse(savedDrafts) : [];
    setDrafts(loadedDrafts);

    const savedHistory = localStorage.getItem('stocktakeHistory');
    const loadedHistory = savedHistory ? JSON.parse(savedHistory) : [];
    setHistory(loadedHistory);

    setStocktakeId(generateNewStocktakeId(loadedHistory, loadedDrafts));
  }, []);

  const resetPage = () => {
    const newId = generateNewStocktakeId(history, drafts);
    setStocktakeId(newId);
    setNotes('');
    setIsProcessing(false);
    setWarehouseScope('all');
    setSelectedWarehouses([]);
    setIncludeMaintenance(false);
    setModelScope('all');
    setSelectedModels([]);
    setScannedItems([]);
    setImeiInput('');
    setResults(null);
    setDuplicateImeis([]);
    setInvalidImeis([]);
    setProcessedSerialsInSession(new Set());
  };

  const handleCreateNew = () => {
    const hasUnsavedData =
      processedSerialsInSession.size > 0 || notes.trim() !== '';

    if (hasUnsavedData) {
      toast({
        title: 'عملية جرد نشطة',
        description: 'يرجى حفظ المسودة الحالية أو إلغائها قبل البدء من جديد.',
        variant: 'destructive',
      });
      return;
    }
    resetPage();
    toast({
      title: 'تم التعيين',
      description: 'تم إعداد صفحة الجرد لعملية جديدة.',
    });
  };

  const getWarehouseName = (id: number | undefined) =>
    warehouses.find((w) => w.id === id)?.name || 'غير محدد';

  const handleStartStocktake = () => {
    if (warehouseScope === 'selected' && selectedWarehouses.length === 0) {
      toast({
        variant: 'destructive',
        title: 'خطأ في النطاق',
        description: 'يرجى اختيار مخزن واحد على الأقل للمتابعة.',
      });
      return;
    }
    if (scannedItems.length === 0) {
      toast({
        variant: 'destructive',
        title: 'القائمة فارغة',
        description: 'يرجى إضافة أجهزة للمطابقة.',
      });
      return;
    }

    setIsProcessing(true);
    
    // Update the set of all processed serials for this session
    const newProcessedSerials = new Set([...processedSerialsInSession, ...scannedItems]);

    const expectedDevices = devices.filter((device) => {
      const inWarehouseScope =
        warehouseScope === 'all' ||
        selectedWarehouses.includes(device.warehouseId ?? -1);
      const isMaintenanceScope =
        includeMaintenance &&
        ['تحتاج صيانة', 'قيد الإصلاح'].includes(device.status);
      const inModelScope =
        modelScope === 'all' ||
        selectedModels.includes(parseInt(device.model.replace(/\D/g, ''))); // Simplified model ID parsing

      return (inWarehouseScope || isMaintenanceScope) && inModelScope;
    });

    const expectedDeviceIds = new Set(expectedDevices.map((d) => d.id));

    // Initialize with existing results or create a new object
    const currentResults = results ? JSON.parse(JSON.stringify(results)) : { ...initialResultsState };

    // Process only the newly scanned items
    scannedItems.forEach((imei) => {
      const device = devices.find((d) => d.id === imei);
      if (device) {
        if (device.status === 'مباع') {
          currentResults.soldButFound.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else if (
          ['تحتاج صيانة', 'قيد الإصلاح'].includes(device.status)
        ) {
          currentResults.inMaintenance.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else if (expectedDeviceIds.has(imei)) {
          currentResults.matching.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
          });
        } else {
          currentResults.extra.push({
            deviceId: device.id,
            model: device.model,
            status: device.status,
            warehouseName: getWarehouseName(device.warehouseId),
            note: 'موجود في موقع غير متوقع',
          });
        }
      } else {
        currentResults.extra.push({
          deviceId: imei,
          model: 'جهاز غير مسجل',
          status: 'غير معروف',
          note: 'غير مسجل بالنظام',
        });
      }
    });
    
    // Recalculate missing based on all processed serials
    currentResults.missing = expectedDevices
      .filter((d) => !newProcessedSerials.has(d.id))
      .map((d) => ({
        deviceId: d.id,
        model: d.model,
        status: d.status,
        warehouseName: getWarehouseName(d.warehouseId),
      }));

    currentResults.expectedCount = expectedDevices.length;

    setResults(currentResults);
    setScannedItems([]); // Clear the temporary list of scanned items
    setProcessedSerialsInSession(newProcessedSerials); // Update the session's master list

    setIsProcessing(false);
    toast({
      title: 'اكتملت المطابقة',
      description: 'تم تحديث النتائج. قائمة الجرد جاهزة لدفعة جديدة.',
    });
  };

  const handleAddImei = () => {
    if (!imeiInput.trim()) return;
    const imei = imeiInput.trim();
    if (processedSerialsInSession.has(imei) || scannedItems.includes(imei)) {
      setDuplicateImeis((prev) => [...prev, imei]);
      toast({
        variant: 'destructive',
        title: 'IMEI مكرر',
        description: 'تم مسح هذا الرقم التسلسلي بالفعل في هذه الجلسة.',
      });
      setImeiInput('');
      return;
    }
    setScannedItems((prev) => [...prev, imei]);
    setImeiInput('');
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const text = e.target?.result as string;
      addItemsFromText(text);
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const handlePastedText = () => {
    addItemsFromText(pastedImeis);
    setPastedImeis('');
    setIsPasteModalOpen(false);
  };

  const addItemsFromText = (text: string) => {
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter(Boolean);
    const existingAndProcessed = new Set([
      ...scannedItems,
      ...processedSerialsInSession,
    ]);

    const newItems: string[] = [];
    const newDuplicates: string[] = [];
    const newInvalids: string[] = [];

    lines.forEach((line) => {
      if (line.length < 5) { // Simple validation
        newInvalids.push(line);
      } else if (existingAndProcessed.has(line)) {
        newDuplicates.push(line);
      } else {
        newItems.push(line);
        existingAndProcessed.add(line);
      }
    });

    setScannedItems((prev) => [...prev, ...newItems]);
    setDuplicateImeis((prev) => [...prev, ...newDuplicates]);
    setInvalidImeis((prev) => [...prev, ...newInvalids]);

    toast({
      title: 'اكتملت الإضافة',
      description: `تمت إضافة ${newItems.length} جهازًا. تم تجاهل ${newDuplicates.length} مكرر و ${newInvalids.length} غير صالح.`,
    });
  };


  const handleRemoveImei = (imei: string) => {
    setScannedItems((p) => p.filter((i) => i !== imei));
  };

  const handleSaveDraftClick = () => {
    if (
      scannedItems.length === 0 &&
      processedSerialsInSession.size === 0 &&
      !notes
    ) {
      toast({
        title: 'لا يوجد ما يمكن حفظه',
        description: 'الرجاء إضافة أجهزة أو ملاحظات أولاً.',
        variant: 'destructive',
      });
      return;
    }
    const draftData: StocktakeDraft = {
      id: stocktakeId,
      scope: {
        warehouseIds: selectedWarehouses,
        deviceModelIds: selectedModels,
        includeMaintenance,
      },
      notes,
      processedSerialNumbers: [...processedSerialsInSession, ...scannedItems],
      lastSavedAt: new Date().toISOString(),
      result: results || initialResultsState,
    };
    const updatedDrafts = [
      ...drafts.filter((d) => d.id !== draftData.id),
      draftData,
    ];
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    toast({
      title: 'تم الحفظ كمسودة',
      description: `تم حفظ عملية الجرد رقم ${stocktakeId} بنجاح.`,
    });
  };

  const handleResumeDraft = (draftToResume: StocktakeHistory | StocktakeDraft) => {
      resetPage(); 
      setTimeout(() => {
        const draft = { ...draftToResume };
        setStocktakeId(draft.id);
        setNotes('notes' in draft ? draft.notes || '' : '');
        setSelectedWarehouses(draft.scope.warehouseIds || []);
        setWarehouseScope((draft.scope.warehouseIds?.length ?? 0) > 0 ? 'selected' : 'all');
        setSelectedModels(draft.scope.deviceModelIds || []);
        setModelScope((draft.scope.deviceModelIds?.length ?? 0) > 0 ? 'selected' : 'all');
        setIncludeMaintenance(draft.scope.includeMaintenance || false);
        setScannedItems([]);

        const processed =
          'processedSerialNumbers' in draft && draft.processedSerialNumbers
            ? draft.processedSerialNumbers
            : draft.result
              ? Object.values(draft.result).filter(Array.isArray).flatMap((cat: any) => (cat || []).map((i: any) => i.deviceId))
              : [];
        
        setProcessedSerialsInSession(new Set(processed));
        
        const fullResult: StocktakeResults = {
            expectedCount: draft.result?.expectedCount || 0,
            matching: draft.result?.matching || [],
            missing: draft.result?.missing || [],
            extra: draft.result?.extra || [],
            soldButFound: draft.result?.soldButFound || [],
            inMaintenance: draft.result?.inMaintenance || [],
        };
        setResults(fullResult);

        setDuplicateImeis([]);
        setInvalidImeis([]);
        setIsDraftsModalOpen(false);
        setShowHistoryDetailsModal(false);
        toast({
          title: 'تم استئناف العملية',
          description: `تم استئناف عملية الجرد رقم ${draft.id}.`,
        });
      }, 100); 
  };


  const handleDeleteDraft = (draftId: string) => {
    const updatedDrafts = drafts.filter((d) => d.id !== draftId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    toast({
      title: 'تم حذف المسودة',
      variant: 'destructive',
    });
  };

  const handleCompleteAndSave = () => {
    if (!results) {
      toast({
        title: 'خطأ',
        description: 'يرجى إجراء المطابقة أولاً قبل حفظ النتائج.',
        variant: 'destructive',
      });
      return;
    }

    const newHistoryEntry: StocktakeHistory = {
      id: stocktakeId,
      completedAt: new Date().toISOString(),
      userName: currentUser?.name || 'غير معروف',
      userId: currentUser?.id || 0,
      scope: {
        warehouseIds: selectedWarehouses,
        deviceModelIds: selectedModels,
        includeMaintenance: includeMaintenance,
      },
      summary: {
        expected: results.expectedCount,
        matched: results.matching.length,
        missing: results.missing.length,
        extra: results.extra.length,
        misplaced:
          results.extra.filter((d) => d.note !== 'غير مسجل بالنظام').length,
        sold: results.soldButFound.length,
        inMaintenance: results.inMaintenance.length,
      },
      result: results,
    };

    const existingHistoryIndex = history.findIndex((h) => h.id === stocktakeId);
    let updatedHistory;

    if (existingHistoryIndex > -1) {
      updatedHistory = [...history];
      updatedHistory[existingHistoryIndex] = newHistoryEntry;
    } else {
      updatedHistory = [...history, newHistoryEntry];
    }
    
    setHistory(updatedHistory.sort((a,b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()));
    localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));

    const updatedDrafts = drafts.filter((d) => d.id !== stocktakeId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));

    toast({
      title: 'تم الحفظ بنجاح',
      description: 'تم حفظ نتائج الجرد في سجل العمليات.',
    });

    resetPage();
  };
  
  const handleDeleteStocktake = () => {
    // Remove from drafts
    const updatedDrafts = drafts.filter((d) => d.id !== stocktakeId);
    setDrafts(updatedDrafts);
    localStorage.setItem('stocktakeDrafts', JSON.stringify(updatedDrafts));
    
    // Remove from history
    const updatedHistory = history.filter((h) => h.id !== stocktakeId);
    setHistory(updatedHistory);
    localStorage.setItem('stocktakeHistory', JSON.stringify(updatedHistory));

    resetPage();
    setIsDeleteAlertOpen(false);
    
    toast({
      title: 'تم الحذف',
      description: 'تم حذف أمر الجرد نهائياً.',
      variant: 'destructive',
    });
  };

  const openDetailsDialog = (title: string, items: string[]) => {
    setDialogTitle(title);
    setDialogItems(items);
    setShowDetailsDialog(true);
  };

  const handleResultCardClick = (
    title: string,
    items: StocktakeResultList[] | undefined,
  ) => {
    if (!items) {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
      return;
    }
    if (items.length > 0) {
      setResultDetails({ title, items });
    } else {
      toast({ title: 'لا توجد بيانات', description: 'هذه القائمة فارغة.' });
    }
  };

  const renderResultCard = (
    title: string,
    count: number,
    icon: React.ReactNode,
    className: string,
    onClick: () => void,
  ) => (
    <Card
      className={cn(
        'cursor-pointer transition-all hover:shadow-md hover:-translate-y-1',
        className,
      )}
      onClick={onClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{count}</div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <ResultSummaryDialog
        isOpen={!!resultDetails}
        onClose={() => setResultDetails(null)}
        details={resultDetails}
      />
      <CumulativeSummaryDialog
        isOpen={isSummaryModalOpen}
        onClose={() => setIsSummaryModalOpen(false)}
        results={results}
        processedSerials={processedSerialsInSession}
        devices={devices}
        warehouses={warehouses}
      />
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>عملية جرد جديدة</CardTitle>
            <CardDescription>
              حدد نطاق الجرد، أدخل الأجهزة، ثم ابدأ المطابقة لعرض النتائج.
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button onClick={handleCreateNew}>
              <PlusCircle className="ml-2 h-4 w-4" /> إنشاء عملية جديدة
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsHistoryModalOpen(true)}
            >
              <FolderOpen className="ml-2 h-4 w-4" /> عرض العمليات السابقة
            </Button>
            <Button
              variant="outline"
              onClick={() => setIsDraftsModalOpen(true)}
            >
              <FolderOpen className="ml-2 h-4 w-4" /> فتح مسودة
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 border p-4 rounded-lg">
            <div className="space-y-2">
              <Label htmlFor="stocktakeId">رقم عملية الجرد</Label>
              <Input id="stocktakeId" value={stocktakeId} disabled />
            </div>
            <div className="space-y-2">
              <Label>المستخدم المسؤول</Label>
              <Input value={currentUser?.name || 'مدير النظام'} disabled />
            </div>
            <div className="space-y-2">
              <Label htmlFor="stocktakeDate">تاريخ الجرد</Label>
              <Input
                id="stocktakeDate"
                type="date"
                defaultValue={new Date().toISOString().split('T')[0]}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes-input">ملاحظات</Label>
              <Input
                id="notes-input"
                placeholder="ملاحظات على عملية الجرد"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
              />
            </div>
          </div>
          <div className="border p-4 rounded-lg space-y-4">
            <h3 className="font-semibold">تحديد نطاق الجرد</h3>
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>المخازن</Label>
                <Select
                  value={warehouseScope}
                  onValueChange={(v) => setWarehouseScope(v as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">كل المخازن</SelectItem>
                    <SelectItem value="selected">تحديد مخازن</SelectItem>
                  </SelectContent>
                </Select>
                {warehouseScope === 'selected' && (
                  <div className="p-2 border rounded-md max-h-32 overflow-y-auto">
                    {warehouses.map((w) => (
                      <div key={w.id} className="flex items-center gap-2 p-1">
                        <input
                          type="checkbox"
                          id={`wh-${w.id}`}
                          checked={selectedWarehouses.includes(w.id)}
                          onChange={(e) =>
                            setSelectedWarehouses((p) =>
                              e.target.checked
                                ? [...p, w.id]
                                : p.filter((id) => id !== w.id),
                            )
                          }
                        />
                        <Label htmlFor={`wh-${w.id}`}>{w.name}</Label>
                      </div>
                    ))}
                  </div>
                )}
                <div className="flex items-center gap-2 pt-2">
                  <input
                    type="checkbox"
                    id="inc-maint"
                    checked={includeMaintenance}
                    onChange={(e) => setIncludeMaintenance(e.target.checked)}
                  />
                  <Label htmlFor="inc-maint">تضمين قسم الصيانة</Label>
                </div>
              </div>
              <div className="space-y-2">
                <Label>الموديلات</Label>
                <Select
                  value={modelScope}
                  onValueChange={(v) => setModelScope(v as any)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">كل الموديلات</SelectItem>
                    <SelectItem value="selected">تحديد موديلات</SelectItem>
                  </SelectContent>
                </Select>
                {modelScope === 'selected' && (
                  <Button
                    variant="outline"
                    onClick={() => setShowModelDialog(true)}
                  >
                    اختر الموديلات ({selectedModels.length})
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>قائمة الجرد الفعلية</CardTitle>
            <div className="flex items-center gap-4 text-sm">
              <span>
                الإجمالي:{' '}
                <Badge className="bg-blue-500/20 text-blue-400">
                  {processedSerialsInSession.size + scannedItems.length}
                </Badge>
              </span>
              <Button
                variant="link"
                className="p-0 h-auto"
                onClick={() =>
                  openDetailsDialog('الأرقام المكررة', duplicateImeis)
                }
                disabled={duplicateImeis.length === 0}
              >
                مكرر:{' '}
                <Badge variant="destructive" className="mr-1">
                  {duplicateImeis.length}
                </Badge>
              </Button>
              <Button
                variant="link"
                className="p-0 h-auto"
                onClick={() =>
                  openDetailsDialog('الأرقام غير الصالحة', invalidImeis)
                }
                disabled={invalidImeis.length === 0}
              >
                غير صالح:{' '}
                <Badge variant="secondary" className="mr-1">
                  {invalidImeis.length}
                </Badge>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="imei-input">إدخال IMEI يدوي أو مسح باركود</Label>
              <div className="flex gap-2">
                <Input
                  id="imei-input"
                  placeholder="امسح أو أدخل الرقم التسلسلي"
                  value={imeiInput}
                  onChange={(e) => setImeiInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddImei()}
                />
                <Button onClick={handleAddImei}>إضافة</Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label>استيراد أو لصق قائمة</Label>
              <div className="flex gap-2">
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileImport}
                  className="hidden"
                  accept=".txt,.csv"
                />
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => fileInputRef.current?.click()}
                  title="استيراد من ملف"
                >
                  <Upload className="h-4 w-4 ml-2" /> استيراد من ملف
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => setIsPasteModalOpen(true)}
                  title="لصق قائمة من الحافظة"
                >
                  <ClipboardPaste className="h-4 w-4 ml-2" /> لصق قائمة
                </Button>
              </div>
            </div>
          </div>
          <div className="mt-4 border rounded-md p-2 h-64 overflow-y-auto">
            <h4 className="font-semibold mb-2">
              الأجهزة المضافة في الدفعة الحالية ({scannedItems.length})
            </h4>
            {scannedItems.length > 0 ? (
              <ul className="space-y-1 text-sm font-mono">
                {scannedItems.map((imei) => (
                  <li
                    key={imei}
                    className="flex justify-between items-center p-1 hover:bg-muted rounded"
                  >
                    <span>{imei}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => handleRemoveImei(imei)}
                    >
                      <Trash2 className="h-3 w-3 text-destructive" />
                    </Button>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-center text-muted-foreground pt-10">
                لم يتم إضافة أجهزة بعد.
              </p>
            )}
          </div>
        </CardContent>
        <CardFooter className="justify-between">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSaveDraftClick}>
              <Save className="h-4 w-4 ml-2" />
              حفظ كمسودة
            </Button>
            <Button
              variant="destructive"
              onClick={() => setIsCancelAlertOpen(true)}
            >
              <X className="ml-2 h-4 w-4" /> إلغاء الأمر
            </Button>
          </div>
          <Button
            onClick={handleStartStocktake}
            disabled={isProcessing || scannedItems.length === 0}
          >
            <ClipboardList className="h-4 w-4 ml-2" />
            {isProcessing
              ? 'جاري المعالجة...'
              : `بدء المطابقة (${scannedItems.length})`}
          </Button>
        </CardFooter>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>نتائج الجرد</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
              {renderResultCard(
                'ملخص الجرد التراكمي',
                processedSerialsInSession.size,
                <Package className="h-4 w-4 text-gray-500" />,
                'bg-gray-500/10 border-gray-500/20',
                () => setIsSummaryModalOpen(true),
              )}
              {renderResultCard(
                'أجهزة مطابقة',
                results.matching.length,
                <Check className="h-4 w-4 text-green-500" />,
                'bg-green-500/10 border-green-500/20',
                () =>
                  handleResultCardClick(
                    'تفاصيل الأجهزة المطابقة',
                    results.matching,
                  ),
              )}
              {renderResultCard(
                'أجهزة مفقودة',
                results.missing.length,
                <X className="h-4 w-4 text-red-500" />,
                'bg-red-500/10 border-red-500/20',
                () =>
                  handleResultCardClick(
                    'تفاصيل الأجهزة المفقودة',
                    results.missing,
                  ),
              )}
              {renderResultCard(
                'أجهزة زائدة/خاطئة',
                results.extra.length,
                <AlertTriangle className="h-4 w-4 text-yellow-500" />,
                'bg-yellow-500/10 border-yellow-500/20',
                () =>
                  handleResultCardClick(
                    'تفاصيل الأجهزة الزائدة أو في غير مكانها',
                    results.extra,
                  ),
              )}
              {renderResultCard(
                'مباعة وموجودة',
                results.soldButFound.length,
                <ShoppingCart className="h-4 w-4 text-purple-500" />,
                'bg-purple-500/10 border-purple-500/20',
                () =>
                  handleResultCardClick(
                    'تفاصيل الأجهزة المباعة والموجودة',
                    results.soldButFound,
                  ),
              )}
              {renderResultCard(
                'موجودة في الصيانة',
                results.inMaintenance.length,
                <Wrench className="h-4 w-4 text-blue-500" />,
                'bg-blue-500/10 border-blue-500/20',
                () =>
                  handleResultCardClick(
                    'تفاصيل الأجهزة الموجودة في الصيانة',
                    results.inMaintenance,
                  ),
              )}
            </div>
          </CardContent>
          <CardFooter className="justify-between">
            <div className="flex gap-2">
              <Button
                variant="destructive"
                onClick={() => setIsDeleteAlertOpen(true)}
              >
                <Trash2 className="ml-2 h-4 w-4" /> حذف الأمر
              </Button>
            </div>
            <Button onClick={handleCompleteAndSave}>
              <Save className="h-4 w-4 ml-2" />
              إكمال وحفظ
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Dialog for result details */}
      <CommandDialog open={showModelDialog} onOpenChange={setShowModelDialog}>
        <CommandInput placeholder="ابحث عن موديل..." />
        <CommandList>
          <CommandEmpty>لم يتم العثور على موديلات.</CommandEmpty>
          <CommandGroup heading="الموديلات المتاحة">
            {/* Populate with models from store */}
          </CommandGroup>
        </CommandList>
      </CommandDialog>

      <Dialog open={isDraftsModalOpen} onOpenChange={setIsDraftsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>المسودات المحفوظة</DialogTitle>
          </DialogHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>رقم الجرد</TableHead>
                <TableHead>تاريخ الحفظ</TableHead>
                <TableHead>عدد الأجهزة</TableHead>
                <TableHead>إجراء</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {drafts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={4} className="text-center">
                    لا توجد مسودات.
                  </TableCell>
                </TableRow>
              ) : (
                drafts.map((draft) => (
                  <TableRow key={draft.id}>
                    <TableCell>{draft.id}</TableCell>
                    <TableCell>
                      {new Date(draft.lastSavedAt).toLocaleString()}
                    </TableCell>
                    <TableCell>{draft.processedSerialNumbers.length}</TableCell>
                    <TableCell className="flex gap-2">
                      <Button size="sm" onClick={() => handleResumeDraft(draft)}>
                        استئناف
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteDraft(draft.id)}
                      >
                        حذف
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </DialogContent>
      </Dialog>

      <Dialog open={isHistoryModalOpen} onOpenChange={setIsHistoryModalOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>سجل عمليات الجرد السابقة</DialogTitle>
          </DialogHeader>
          <div className="max-h-[60vh] overflow-y-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم العملية</TableHead>
                  <TableHead>المخزن</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>المستخدم</TableHead>
                  <TableHead>مطابق</TableHead>
                  <TableHead>مفقود</TableHead>
                  <TableHead>زائد</TableHead>
                  <TableHead>إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {history.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="h-24 text-center">
                      لا يوجد سجل لعرضه.
                    </TableCell>
                  </TableRow>
                ) : (
                  history.map((hist) => (
                    <TableRow key={hist.id}>
                      <TableCell>{hist.id}</TableCell>
                      <TableCell>
                        {hist.scope.warehouseIds.length > 0
                          ? hist.scope.warehouseIds
                              .map((id) => getWarehouseName(id))
                              .join(', ')
                          : 'كل المخازن'}
                        {hist.scope.includeMaintenance && ' (+ صيانة)'}
                      </TableCell>
                      <TableCell>
                        {new Date(hist.completedAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>{hist.userName}</TableCell>
                      <TableCell className="text-green-500">
                        {hist.summary.matched}
                      </TableCell>
                      <TableCell className="text-red-500">
                        {hist.summary.missing}
                      </TableCell>
                      <TableCell className="text-blue-500">
                        {hist.summary.extra}
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedHistory(hist);
                            setShowHistoryDetailsModal(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </DialogContent>
      </Dialog>

      <HistoryDetailsDialog
        isOpen={showHistoryDetailsModal}
        onClose={() => setShowHistoryDetailsModal(false)}
        historyEntry={selectedHistory}
        onReopen={handleResumeDraft}
      />

      <Dialog open={isPasteModalOpen} onOpenChange={setIsPasteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>لصق قائمة الأرقام التسلسلية</DialogTitle>
            <DialogDescription>
              ألصق قائمة الأرقام التسلسلية هنا، كل رقم في سطر جديد.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="111111111111111
222222222222222
333333333333333"
              className="h-48 font-mono"
              value={pastedImeis}
              onChange={(e) => setPastedImeis(e.target.value)}
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsPasteModalOpen(false)}
            >
              إلغاء
            </Button>
            <Button onClick={handlePastedText}>إضافة للقائمة</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <AlertDialog open={isCancelAlertOpen} onOpenChange={setIsCancelAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الإلغاء؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيتم فقدان جميع البيانات غير المحفوظة في أمر الجرد الحالي.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                resetPage();
                setIsCancelAlertOpen(false);
              }}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الإلغاء
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <AlertDialog open={isDeleteAlertOpen} onOpenChange={setIsDeleteAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>هل أنت متأكد من الحذف؟</AlertDialogTitle>
            <AlertDialogDescription>
              سيؤدي هذا الإجراء إلى حذف أمر الجرد الحالي من المسودات نهائيًا.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>تراجع</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteStocktake}
              className="bg-destructive hover:bg-destructive/90"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <Dialog open={showDetailsDialog} onOpenChange={setShowDetailsDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>{dialogTitle}</DialogTitle>
          </DialogHeader>
          <ScrollArea className="max-h-80">
            <ul className="space-y-1 font-mono text-sm text-muted-foreground">
              {dialogItems.map((item, index) => (
                <li key={index}>{item}</li>
              ))}
            </ul>
          </ScrollArea>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowDetailsDialog(false)}
            >
              إغلاق
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
