import { NextResponse } from 'next/server';
import { initialUsers } from '@/lib/data';
import { User, AuditLog } from '@/lib/types';

let users: User[] = [...initialUsers];

async function logAudit(
  operation: string,
  details: string,
  userId: number,
  username: string,
) {
  const logEntry: AuditLog = {
    id: '', // Will be generated by the API
    timestamp: new Date(),
    userId,
    username,
    operation,
    details,
  };
  try {
    await fetch('http://localhost:3000/api/audit-logs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(logEntry),
    });
  } catch (error) {
    console.error('Failed to log audit entry:', error);
  }
}

export async function GET() {
  return NextResponse.json(users);
}

export async function POST(request: Request) {
  const newUser: User = await request.json();
  // Basic validation and ID assignment
  if (!newUser.name || !newUser.username || !newUser.email) {
    return NextResponse.json(
      { message: 'Name, username, and email are required' },
      { status: 400 },
    );
  }
  if (users.some((u) => u.email === newUser.email)) {
    return NextResponse.json(
      { message: 'Email already exists' },
      { status: 409 },
    );
  }
  if (users.some((u) => u.username === newUser.username)) {
    return NextResponse.json(
      { message: 'Username already exists' },
      { status: 409 },
    );
  }
  newUser.id = users.length > 0 ? Math.max(...users.map((u) => u.id)) + 1 : 1;
  users.push(newUser);
  await logAudit(
    'User Added',
    `New user ${newUser.name} (${newUser.email}) added.`,
    newUser.id,
    newUser.name,
  );
  return NextResponse.json(newUser, { status: 201 });
}

export async function PUT(request: Request) {
  const updatedUser: User = await request.json();
  if (!updatedUser.id) {
    return NextResponse.json(
      { message: 'User ID is required' },
      { status: 400 },
    );
  }

  const userIndex = users.findIndex((u) => u.id === updatedUser.id);
  if (userIndex === -1) {
    return NextResponse.json({ message: 'User not found' }, { status: 404 });
  }

  // Prevent changing email to an existing one (excluding the user being updated)
  if (
    updatedUser.email &&
    users.some((u) => u.email === updatedUser.email && u.id !== updatedUser.id)
  ) {
    return NextResponse.json(
      { message: 'Email already exists' },
      { status: 409 },
    );
  }

  users[userIndex] = { ...users[userIndex], ...updatedUser };
  await logAudit(
    'User Updated',
    `User ${updatedUser.name} (${updatedUser.id}) updated.`,
    updatedUser.id,
    updatedUser.name,
  );
  return NextResponse.json(users[userIndex]);
}

export async function DELETE(request: Request) {
  const { id } = await request.json();

  if (!id) {
    return NextResponse.json(
      { message: 'User ID is required' },
      { status: 400 },
    );
  }

  // Protect Super Admin (assuming Super Admin has id: 1)
  if (id === 1) {
    return NextResponse.json(
      { message: 'Cannot delete or disable Super Admin' },
      { status: 403 },
    );
  }

  const userIndex = users.findIndex((u) => u.id === id);
  if (userIndex === -1) {
    return NextResponse.json({ message: 'User not found' }, { status: 404 });
  }

  // Soft delete: Mark as inactive instead of removing
  users[userIndex] = { ...users[userIndex], status: 'Inactive' }; // Assuming a 'status' field
  await logAudit(
    'User Disabled',
    `User ${users[userIndex].name} (${users[userIndex].id}) disabled.`,
    users[userIndex].id,
    users[userIndex].name,
  );
  return NextResponse.json({ message: 'User disabled successfully' });
}
