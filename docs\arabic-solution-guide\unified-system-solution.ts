// الطريقة الرابعة: نظام موحد قابل لإعادة الاستخدام
// ملف: hooks/useArabicPrintExport.ts

import { useState } from 'react';

// 1. تعريف أنواع البيانات
export interface ArabicPrintData {
  title: string;
  subtitle?: string;
  sections: ArabicPrintSection[];
  metadata?: Record<string, any>;
}

export interface ArabicPrintSection {
  title: string;
  type: 'info' | 'table' | 'timeline' | 'grid' | 'custom';
  data: any;
  columns?: string[];
  className?: string;
}

export interface ArabicPrintOptions {
  fileName?: string;
  title?: string;
  includeTimestamp?: boolean;
  pageSize?: 'A4' | 'A3' | 'Letter';
  orientation?: 'portrait' | 'landscape';
  method?: 'canvas' | 'html' | 'jspdf';
}

// 2. Hook رئيسي للطباعة والتصدير
export function useArabicPrintExport() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // وظيفة الطباعة الذكية
  const printData = async (
    data: ArabicPrintData,
    options: ArabicPrintOptions = {}
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      const method = options.method || 'canvas'; // Canvas كافتراضي للأفضلية

      switch (method) {
        case 'canvas':
          await printWithCanvas(data, options);
          break;
        case 'html':
          await printWithHTML(data, options);
          break;
        case 'jspdf':
          await printWithJsPDF(data, options);
          break;
        default:
          throw new Error('طريقة طباعة غير مدعومة');
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء الطباعة';
      setError(errorMessage);
      console.error('Print error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // وظيفة التصدير الذكية
  const exportToPDF = async (
    data: ArabicPrintData,
    options: ArabicPrintOptions = {}
  ) => {
    try {
      setIsLoading(true);
      setError(null);

      // استخدام Canvas للتصدير (أفضل جودة للعربية)
      await exportWithCanvas(data, options);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'حدث خطأ أثناء التصدير';
      setError(errorMessage);
      console.error('Export error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return {
    printData,
    exportToPDF,
    isLoading,
    error,
    clearError: () => setError(null)
  };
}

// 3. وظائف الطباعة المختلفة
async function printWithCanvas(data: ArabicPrintData, options: ArabicPrintOptions) {
  // استيراد ديناميكي لوحدة Canvas
  const { createArabicPDFWithCanvas } = await import('./canvas-pdf-solution');
  
  await createArabicPDFWithCanvas(
    convertToCanvasFormat(data),
    options.fileName || 'report',
    options.title || data.title
  );
}

async function printWithHTML(data: ArabicPrintData, options: ArabicPrintOptions) {
  // استيراد ديناميكي لوحدة HTML
  const { printArabicContent } = await import('./html-print-solution');
  
  printArabicContent(
    convertToHTMLFormat(data),
    options.title || data.title
  );
}

async function printWithJsPDF(data: ArabicPrintData, options: ArabicPrintOptions) {
  // استيراد ديناميكي لوحدة jsPDF
  const { createArabicPDF } = await import('./jspdf-arabic-solution');
  
  createArabicPDF(
    convertToJsPDFFormat(data),
    options.fileName || 'report',
    options.title || data.title
  );
}

async function exportWithCanvas(data: ArabicPrintData, options: ArabicPrintOptions) {
  // نفس وظيفة Canvas ولكن للتصدير
  await printWithCanvas(data, options);
}

// 4. وظائف تحويل البيانات
function convertToCanvasFormat(data: ArabicPrintData): any {
  const infoSection = data.sections.find(s => s.type === 'info');
  const tableSection = data.sections.find(s => s.type === 'table');
  
  return {
    name: infoSection?.data?.['الاسم'] || data.title,
    date: infoSection?.data?.['التاريخ'] || new Date().toLocaleDateString('ar-EG'),
    description: infoSection?.data?.['الوصف'] || data.subtitle || '',
    items: tableSection?.data || []
  };
}

function convertToHTMLFormat(data: ArabicPrintData): any {
  return convertToCanvasFormat(data); // نفس التنسيق
}

function convertToJsPDFFormat(data: ArabicPrintData): any {
  return convertToCanvasFormat(data); // نفس التنسيق
}

// 5. قوالب جاهزة للأقسام المختلفة
export const ArabicTemplates = {
  // قالب التوريد
  supply: (orderData: any): ArabicPrintData => ({
    title: `أمر التوريد رقم ${orderData.id}`,
    subtitle: `تاريخ التوريد: ${orderData.date}`,
    sections: [
      {
        title: 'معلومات المورد',
        type: 'info',
        data: {
          'اسم المورد': orderData.supplierName,
          'رقم الهاتف': orderData.supplierPhone,
          'العنوان': orderData.supplierAddress
        }
      },
      {
        title: 'تفاصيل الطلب',
        type: 'info',
        data: {
          'رقم الطلب': orderData.id,
          'تاريخ التوريد': orderData.date,
          'المخزن': orderData.warehouse,
          'المسؤول': orderData.employee
        }
      },
      {
        title: 'الأصناف المطلوبة',
        type: 'table',
        data: orderData.items,
        columns: ['الصنف', 'الكمية', 'الحالة', 'الرقم التسلسلي']
      }
    ]
  }),

  // قالب المبيعات
  sales: (invoiceData: any): ArabicPrintData => ({
    title: `فاتورة مبيعات رقم ${invoiceData.number}`,
    subtitle: `تاريخ الفاتورة: ${invoiceData.date}`,
    sections: [
      {
        title: 'معلومات العميل',
        type: 'info',
        data: {
          'اسم العميل': invoiceData.customerName,
          'رقم الهاتف': invoiceData.customerPhone,
          'العنوان': invoiceData.customerAddress
        }
      },
      {
        title: 'الأصناف المباعة',
        type: 'table',
        data: invoiceData.items,
        columns: ['الصنف', 'الكمية', 'السعر', 'الإجمالي']
      }
    ]
  }),

  // قالب المخزون
  inventory: (inventoryData: any): ArabicPrintData => ({
    title: `تقرير المخزون - ${inventoryData.warehouseName}`,
    subtitle: `تاريخ التقرير: ${inventoryData.date}`,
    sections: [
      {
        title: 'ملخص المخزون',
        type: 'info',
        data: {
          'إجمالي الأصناف': inventoryData.totalItems,
          'القيمة الإجمالية': inventoryData.totalValue,
          'الأصناف المنخفضة': inventoryData.lowStockItems
        }
      },
      {
        title: 'تفاصيل الأصناف',
        type: 'table',
        data: inventoryData.items,
        columns: ['الصنف', 'الكمية المتاحة', 'الحد الأدنى', 'الحالة']
      }
    ]
  })
};

// 6. مكونات React جاهزة
export function ArabicPrintButton({ 
  data, 
  options = {}, 
  children = 'طباعة',
  className = ''
}: {
  data: ArabicPrintData;
  options?: ArabicPrintOptions;
  children?: React.ReactNode;
  className?: string;
}) {
  const { printData, isLoading } = useArabicPrintExport();

  return (
    <button
      onClick={() => printData(data, options)}
      disabled={isLoading}
      className={`print-button ${className}`}
    >
      {isLoading ? 'جاري الطباعة...' : children}
    </button>
  );
}

export function ArabicExportButton({ 
  data, 
  options = {}, 
  children = 'تصدير PDF',
  className = ''
}: {
  data: ArabicPrintData;
  options?: ArabicPrintOptions;
  children?: React.ReactNode;
  className?: string;
}) {
  const { exportToPDF, isLoading } = useArabicPrintExport();

  return (
    <button
      onClick={() => exportToPDF(data, options)}
      disabled={isLoading}
      className={`export-button ${className}`}
    >
      {isLoading ? 'جاري التصدير...' : children}
    </button>
  );
}

// 7. مثال للاستخدام الكامل
export function SupplyOrderPrintExample({ orderData }: { orderData: any }) {
  const printData = ArabicTemplates.supply(orderData);
  
  return (
    <div className="print-controls">
      <ArabicPrintButton 
        data={printData}
        options={{ 
          method: 'canvas',
          fileName: `supply_order_${orderData.id}`
        }}
      />
      
      <ArabicExportButton 
        data={printData}
        options={{ 
          fileName: `supply_order_${orderData.id}`
        }}
      />
    </div>
  );
}

// المميزات:
// ✅ نظام موحد لجميع أنواع الطباعة
// ✅ قوالب جاهزة للأقسام المختلفة
// ✅ مكونات React قابلة لإعادة الاستخدام
// ✅ دعم متعدد الطرق (Canvas, HTML, jsPDF)
// ✅ سهولة التطبيق في أي مشروع
// ✅ معالجة شاملة للأخطاء
// ✅ حالة تحميل واضحة للمستخدم
